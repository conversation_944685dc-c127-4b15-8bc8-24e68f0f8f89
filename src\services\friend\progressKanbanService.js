import api from '../api';
import { warnIfNonStandardFormat } from '@/utils/apiUtils';

/**
 * 微信产品对接看板服务
 * 提供获取和更新看板数据的功能
 */
class ProgressKanbanService {
  /**
   * 获取看板数据
   * @param {Object} params - 查询参数
   * @param {number} params.微信id - 我方微信账号的ID
   * @returns {Promise<Object>} 按泳道分组的看板数据
   */
  async getBoardData(params) {
    try {
      console.log('POST', '/wechat/board/board-data', params);
      const response = await api.post('/wechat/board/board-data', params);
      
      // 检查格式并发出警告（如果不规范）
      warnIfNonStandardFormat(response, '看板数据');
      
      // 检查是否成功
      if (response && response.status === 100) {
        // 标准格式：优先从data字段获取业务数据
        const lanes = response.data;
        
        // 处理潜在的null值或数据缺失
        if (Array.isArray(lanes)) {
          // 确保所有卡片的owner字段存在
          lanes.forEach(lane => {
            if (lane.cards && Array.isArray(lane.cards)) {
              lane.cards.forEach(card => {
                // 确保卡片有owner字段，使用对方微信号作为主要标识
                if (!card.owner) {
                  const details = card.details || {};
                  card.owner = details.对方微信号 || 
                               details.owner || 
                               `未知好友(ID:${details.对方微信号ID || '未知'})`;
                }
              });
            }
          });
        }
        
        return {
          status: 100,
          data: lanes || []
        };
      }
      
      // 处理业务逻辑失败的情况
      const errorMessage = (response && response.message) || '获取看板数据失败';
      console.error('获取看板数据业务错误:', errorMessage);
      throw new Error(errorMessage);
    } catch (error) {
      console.error('获取看板数据服务异常:', error);
      throw error;
    }
  }

  /**
   * 获取看板统计数据
   * @param {Object} params - 查询参数
   * @param {number} params.微信id - 我方微信账号的ID
   * @param {number} params.统计天数 - 统计最近多少天的数据趋势
   * @returns {Promise<Object>} 统计数据，包含状态统计、阶段统计、分类统计和趋势数据
   */
  async getBoardStats(params) {
    try {
      console.log('POST', '/wechat/board/board-stats', params);
      const response = await api.post('/wechat/board/board-stats', params);
      
      // 检查格式并发出警告（如果不规范）
      warnIfNonStandardFormat(response, '看板统计数据');
      
      // 检查是否成功
      if (response && response.status === 100) {
        return {
          status: 100,
          data: response.data || {
            状态统计: {},
            阶段统计: {},
            分类统计: [],
            趋势数据: []
          }
        };
      }
      
      // 处理业务逻辑失败的情况
      const errorMessage = (response && response.message) || '获取看板统计数据失败';
      console.error('获取看板统计数据业务错误:', errorMessage);
      throw new Error(errorMessage);
    } catch (error) {
      console.error('获取看板统计数据服务异常:', error);
      throw error;
    }
  }

  /**
   * 当卡片被拖动时，更新其状态
   * @param {Object} updateData - 更新所需的数据
   * @param {string} updateData.cardId - 被拖动的卡片ID (即对接进度ID)
   * @param {string} updateData.sourceLaneId - 卡片所在的源泳道ID
   * @param {string} updateData.destinationLaneId - 卡片被拖动到的目标泳道ID
   * @returns {Promise<Object>} 更新操作的结果
   */
  async updateCardStatus(updateData) {
    try {
      // 这是一个模拟的实现，我们将直接返回成功
      // 在实际应用中，这里会调用后端API来更新数据库
      console.log('更新卡片状态:', updateData);
      await new Promise(resolve => setTimeout(resolve, 500)); // 模拟网络延迟
      
      return {
        status: 100,
        message: '卡片状态更新成功',
      };
    } catch (error) {
      console.error('更新卡片状态服务异常:', error);
      throw error;
    }
  }
}

export default new ProgressKanbanService(); 