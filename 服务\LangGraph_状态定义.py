"""
LangGraph状态定义
定义智能体的状态结构和类型
"""

import logging
from typing import Any, Dict, List, Optional, TypedDict, Annotated
from datetime import datetime

# 尝试导入LangGraph组件
try:
    from langgraph.graph.message import add_messages
    from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, ToolMessage
    from langchain_core.documents import Document
    LANGGRAPH_AVAILABLE = True
except ImportError:
    LANGGRAPH_AVAILABLE = False
    # 提供兼容性定义
    BaseMessage = Dict[str, Any]
    HumanMessage = Dict[str, Any]
    AIMessage = Dict[str, Any]
    ToolMessage = Dict[str, Any]
    Document = Dict[str, Any]
    
    def add_messages(existing: List, new: List) -> List:
        """简单的消息合并函数"""
        return existing + new

# 日志配置
状态日志器 = logging.getLogger("LangGraph状态定义")


class 智能体状态(TypedDict):
    """LangGraph智能体状态定义"""
    
    # 核心消息流
    messages: Annotated[List[BaseMessage], add_messages]
    
    # RAG上下文
    context: List[Document]
    
    # 工具调用历史
    tools_used: List[Dict[str, Any]]
    
    # 用户和会话信息
    user_id: int
    agent_id: int
    session_id: str
    thread_id: str
    
    # 执行状态
    current_step: str
    step_count: int
    
    # 自定义变量
    custom_variables: Dict[str, Any]
    
    # 性能统计
    start_time: Optional[datetime]
    total_tokens: int
    tool_calls_count: int


class 工具调用状态(TypedDict):
    """工具调用状态"""
    tool_name: str
    tool_args: Dict[str, Any]
    tool_result: Optional[str]
    execution_id: Optional[int]
    status: str  # pending, running, success, failed
    start_time: datetime
    end_time: Optional[datetime]
    error_message: Optional[str]


class 执行步骤状态(TypedDict):
    """执行步骤状态"""
    step_name: str
    step_type: str
    step_input: Dict[str, Any]
    step_output: Optional[Dict[str, Any]]
    step_id: Optional[int]
    status: str  # pending, running, completed, failed
    start_time: datetime
    end_time: Optional[datetime]
    execution_time: Optional[float]


def 创建初始状态(用户id: int, 智能体id: int, 会话ID: str, 线程ID: str) -> 智能体状态:
    """创建初始的智能体状态"""
    return 智能体状态(
        messages=[],
        context=[],
        tools_used=[],
        user_id=用户id,
        agent_id=智能体id,
        session_id=会话ID,
        thread_id=线程ID,
        current_step="初始化",
        step_count=0,
        custom_variables={},
        start_time=datetime.now(),
        total_tokens=0,
        tool_calls_count=0
    )


def 更新状态消息(状态: 智能体状态, 新消息: BaseMessage) -> 智能体状态:
    """更新状态中的消息"""
    if LANGGRAPH_AVAILABLE:
        状态["messages"] = add_messages(状态["messages"], [新消息])
    else:
        状态["messages"].append(新消息)
    return 状态


def 添加工具调用记录(状态: 智能体状态, 工具调用: 工具调用状态) -> 智能体状态:
    """添加工具调用记录到状态"""
    状态["tools_used"].append(工具调用)
    状态["tool_calls_count"] += 1
    return 状态


def 更新执行步骤(状态: 智能体状态, 步骤名称: str, 步骤类型: str = "custom") -> 智能体状态:
    """更新当前执行步骤"""
    状态["current_step"] = 步骤名称
    状态["step_count"] += 1
    状态日志器.debug(f"执行步骤更新: {步骤名称} (第{状态['step_count']}步)")
    return 状态


def 添加RAG上下文(状态: 智能体状态, 文档列表: List[Document]) -> 智能体状态:
    """添加RAG检索的上下文文档"""
    状态["context"].extend(文档列表)
    状态日志器.debug(f"添加RAG上下文: {len(文档列表)} 个文档")
    return 状态


def 清理状态上下文(状态: 智能体状态, 保留消息数: int = 10) -> 智能体状态:
    """清理状态中的历史数据，保持性能"""
    # 保留最近的消息
    if len(状态["messages"]) > 保留消息数:
        状态["messages"] = 状态["messages"][-保留消息数:]
    
    # 清理过多的上下文文档
    if len(状态["context"]) > 20:
        状态["context"] = 状态["context"][-20:]
    
    # 清理过多的工具调用记录
    if len(状态["tools_used"]) > 50:
        状态["tools_used"] = 状态["tools_used"][-50:]
    
    状态日志器.debug("状态上下文已清理")
    return 状态


def 获取状态摘要(状态: 智能体状态) -> Dict[str, Any]:
    """获取状态摘要信息"""
    return {
        "用户id": 状态["user_id"],
        "智能体id": 状态["agent_id"],
        "线程ID": 状态["thread_id"],
        "当前步骤": 状态["current_step"],
        "步骤计数": 状态["step_count"],
        "消息数量": len(状态["messages"]),
        "上下文文档数": len(状态["context"]),
        "工具调用数": 状态["tool_calls_count"],
        "总Token数": 状态["total_tokens"],
        "运行时间": (datetime.now() - 状态["start_time"]).total_seconds() if 状态["start_time"] else 0
    }


def 验证状态完整性(状态: 智能体状态) -> bool:
    """验证状态的完整性"""
    try:
        必需字段 = ["user_id", "agent_id", "session_id", "thread_id", "messages", "context", "tools_used"]
        
        for 字段 in 必需字段:
            if 字段 not in 状态:
                状态日志器.error(f"状态缺少必需字段: {字段}")
                return False
        
        # 验证数据类型
        if not isinstance(状态["messages"], list):
            状态日志器.error("messages字段必须是列表")
            return False
        
        if not isinstance(状态["context"], list):
            状态日志器.error("context字段必须是列表")
            return False
        
        if not isinstance(状态["tools_used"], list):
            状态日志器.error("tools_used字段必须是列表")
            return False
        
        状态日志器.debug("状态完整性验证通过")
        return True
        
    except Exception as e:
        状态日志器.error(f"状态完整性验证失败: {str(e)}")
        return False


def 状态转换为字典(状态: 智能体状态) -> Dict[str, Any]:
    """将状态转换为可序列化的字典"""
    try:
        序列化状态 = {}
        
        for 键, 值 in 状态.items():
            if 键 == "messages":
                # 序列化消息
                序列化状态[键] = [
                    {
                        "type": getattr(msg, "type", "unknown"),
                        "content": getattr(msg, "content", str(msg)),
                        "additional_kwargs": getattr(msg, "additional_kwargs", {}),
                        "tool_calls": getattr(msg, "tool_calls", [])
                    } if hasattr(msg, "type") else msg
                    for msg in 值
                ]
            elif 键 == "context":
                # 序列化文档
                序列化状态[键] = [
                    {
                        "page_content": getattr(doc, "page_content", str(doc)),
                        "metadata": getattr(doc, "metadata", {})
                    } if hasattr(doc, "page_content") else doc
                    for doc in 值
                ]
            elif 键 == "start_time" and isinstance(值, datetime):
                序列化状态[键] = 值.isoformat()
            else:
                序列化状态[键] = 值
        
        return 序列化状态
        
    except Exception as e:
        状态日志器.error(f"状态序列化失败: {str(e)}")
        return {}


def 字典转换为状态(状态字典: Dict[str, Any]) -> 智能体状态:
    """将字典转换为状态对象"""
    try:
        # 处理特殊字段
        if "start_time" in 状态字典 and isinstance(状态字典["start_time"], str):
            状态字典["start_time"] = datetime.fromisoformat(状态字典["start_time"])
        
        # 确保必需字段存在
        默认值 = {
            "messages": [],
            "context": [],
            "tools_used": [],
            "custom_variables": {},
            "step_count": 0,
            "total_tokens": 0,
            "tool_calls_count": 0
        }
        
        for 键, 默认 in 默认值.items():
            if 键 not in 状态字典:
                状态字典[键] = 默认
        
        return 智能体状态(**状态字典)
        
    except Exception as e:
        状态日志器.error(f"字典转换为状态失败: {str(e)}")
        # 返回一个基本的状态
        return 创建初始状态(
            用户id=状态字典.get("user_id", 0),
            智能体id=状态字典.get("agent_id", 0),
            会话ID=状态字典.get("session_id", ""),
            线程ID=状态字典.get("thread_id", "")
        )
