#!/usr/bin/env python3
"""
查找代码中使用cursor()的MySQL风格代码
这些代码需要修复为PostgreSQL风格
"""

import os
import re
from typing import List, Dict

def find_cursor_issues(directory: str = '.') -> List[Dict]:
    """查找使用cursor()的代码"""
    issues = []
    
    for root, dirs, files in os.walk(directory):
        # 跳过虚拟环境和缓存目录
        if any(skip in root for skip in ['__pycache__', '.venv', '.git', 'node_modules']):
            continue
            
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                    lines = content.split('\n')
                    for line_num, line in enumerate(lines, 1):
                        # 查找cursor()使用
                        if '.cursor()' in line:
                            issues.append({
                                'file': file_path,
                                'line': line_num,
                                'type': 'cursor_usage',
                                'content': line.strip(),
                                'issue': 'MySQL风格的cursor()使用，需要改为PostgreSQL风格'
                            })
                        
                        # 查找cursor相关的with语句
                        if 'cursor' in line and 'with' in line and 'as' in line:
                            issues.append({
                                'file': file_path,
                                'line': line_num,
                                'type': 'cursor_context',
                                'content': line.strip(),
                                'issue': 'MySQL风格的cursor上下文管理器'
                            })
                        
                        # 查找游标相关操作
                        if '游标.' in line:
                            issues.append({
                                'file': file_path,
                                'line': line_num,
                                'type': 'cursor_operation',
                                'content': line.strip(),
                                'issue': 'MySQL风格的游标操作'
                            })
                            
                except Exception as e:
                    print(f"Error reading {file_path}: {e}")
    
    return issues

def main():
    print("查找cursor()相关的MySQL风格代码")
    print("=" * 60)

    # 查找问题
    print("\n正在扫描代码...")
    issues = find_cursor_issues('.')
    
    if not issues:
        print("没有发现cursor相关问题！")
        return
    
    # 过滤项目文件（排除第三方库）
    project_issues = [issue for issue in issues if not any(skip in issue['file'] for skip in ['.venv', '__pycache__'])]
    
    if not project_issues:
        print("项目文件中没有发现cursor相关问题！")
        return
    
    # 按文件分组显示
    files_with_issues = {}
    for issue in project_issues:
        file_path = issue['file']
        if file_path not in files_with_issues:
            files_with_issues[file_path] = []
        files_with_issues[file_path].append(issue)
    
    print(f"\n发现 {len(project_issues)} 个问题，涉及 {len(files_with_issues)} 个文件")
    print("\n详细问题列表：")
    print("=" * 60)
    
    for file_path, file_issues in files_with_issues.items():
        print(f"\n📁 文件: {file_path}")
        print(f"   问题数量: {len(file_issues)}")
        
        for issue in file_issues:
            print(f"   ├─ 行 {issue['line']}: {issue['type']}")
            print(f"   │  代码: {issue['content']}")
            print(f"   │  问题: {issue['issue']}")
            print(f"   │")
    
    print("\n修复建议：")
    print("=" * 60)
    print("1. 将 'async with 连接.cursor() as 游标:' 改为直接使用连接")
    print("2. 将 '游标.execute()' 改为 '连接.execute()' 或 '连接.fetch()'")
    print("3. 将 '游标.fetchall()' 改为 '连接.fetch()'")
    print("4. 将 '游标.fetchone()' 改为 '连接.fetchrow()'")
    print("5. PostgreSQL的asyncpg不需要cursor，直接在连接上操作")
    
    print(f"\n需要修复的文件列表：")
    for file_path in files_with_issues.keys():
        print(f"  - {file_path}")

if __name__ == "__main__":
    main()
