<template>
  <div class="knowledge-management-module">
    <!-- 功能介绍 -->
    <a-alert
      message="知识库管理"
      description="在这里您可以查看和管理所有知识库中的文档，包括从产品管理提交的产品信息文档。您可以编辑文档内容、删除不需要的文档，以及查看文档的详细信息。"
      type="info"
      show-icon
      closable
      class="intro-alert"
    />

    <!-- 知识库选择器 -->
    <a-card title="选择知识库" class="kb-selector-card">
      <a-row :gutter="16">
        <a-col :span="18">
          <a-select
            v-model:value="选中的知识id"
            placeholder="请选择要管理的知识库"
            style="width: 100%"
            :loading="知识库加载中"
            @change="处理知识库变化"
          >
            <a-select-option
              v-for="知识库 in 知识库列表"
              :key="知识库.id"
              :value="知识库.id"
            >
              <div class="知识库选项">
                <a-tag :color="知识库.类型 === '默认' ? 'blue' : 'green'" size="small">
                  {{ 知识库.类型 === '默认' ? '默认' : '自定义' }}
                </a-tag>
                <span>{{ 知识库.名称 }}</span>
                <span class="知识库描述">{{ 知识库.描述 }}</span>
              </div>
            </a-select-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-button @click="刷新知识库列表" :loading="知识库加载中">
            <reload-outlined />
            刷新知识库
          </a-button>
        </a-col>
      </a-row>
    </a-card>

    <!-- 文档管理区域 -->
    <a-card
      v-if="选中的知识库"
      :title="`${选中的知识库.名称} - 文档列表`"
      class="文档列表卡片"
    >
      <template #extra>
        <a-space>
          <a-button @click="刷新文档列表" :loading="文档加载中">
            <reload-outlined />
            刷新
          </a-button>
          <a-button type="primary" @click="显示添加文档弹窗">
            <plus-outlined />
            添加文档
          </a-button>
        </a-space>
      </template>

      <!-- 统计信息 -->
      <div class="统计信息区域">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic title="总文档数" :value="文档统计.总数" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="关联产品" :value="文档统计.产品文档数" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="独立文档" :value="文档统计.自定义文档数" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="最近更新" :value="文档统计.最近更新" />
          </a-col>
        </a-row>
      </div>

      <a-divider />

      <!-- 搜索和筛选 -->
      <div class="search-section">
        <a-row :gutter="16" align="middle">
          <a-col :span="8">
            <a-input-search
              v-model:value="搜索关键词"
              placeholder="搜索文档名称或内容"
              @search="处理搜索"
              allow-clear
            />
          </a-col>
          <a-col :span="6">
            <a-select
              v-model:value="状态筛选"
              placeholder="筛选上传状态"
              style="width: 100%"
              allow-clear
              @change="处理搜索"
            >
              <a-select-option value="处理中">处理中</a-select-option>
              <a-select-option value="处理完成">处理完成</a-select-option>
              <a-select-option value="处理失败">处理失败</a-select-option>
              <a-select-option value="未知">未知</a-select-option>
            </a-select>
          </a-col>
          <a-col :span="6">
            <a-select
              v-model:value="类型筛选"
              placeholder="筛选文件类型"
              style="width: 100%"
              allow-clear
              @change="处理搜索"
            >
              <a-select-option value="text">文本</a-select-option>
              <a-select-option value="pdf">PDF</a-select-option>
              <a-select-option value="doc">Word</a-select-option>
              <a-select-option value="excel">Excel</a-select-option>
            </a-select>
          </a-col>
          <a-col :span="4">
            <a-button @click="重置筛选条件">
              <clear-outlined />
              重置
            </a-button>
          </a-col>
        </a-row>
      </div>

      <!-- 操作工具栏 -->
      <div class="action-toolbar" style="margin: 16px 0;">
        <a-row justify="space-between" align="middle">
          <a-col>
            <a-space>
              <a-button type="primary" @click="显示添加文档弹窗">
                <plus-outlined />
                添加文档
              </a-button>
              <a-button
                danger
                :disabled="!选中的文档行 || 选中的文档行.length === 0"
                @click="批量删除文档"
              >
                <delete-outlined />
                批量删除 ({{ 选中的文档行?.length || 0 }})
              </a-button>
              <a-button @click="刷新文档列表">
                <reload-outlined />
                刷新
              </a-button>
            </a-space>
          </a-col>
          <a-col>
            <a-space>
              <span class="text-muted">
                共 {{ 过滤后的文档列表.length }} 个文档
                <span v-if="选中的文档行 && 选中的文档行.length > 0">
                  ，已选中 {{ 选中的文档行.length }} 个
                </span>
              </span>
            </a-space>
          </a-col>
        </a-row>
      </div>

      <!-- 文档表格 -->
      <a-table
        :columns="文档表格列配置"
        :data-source="过滤后的文档列表"
        :pagination="文档分页配置"
        :loading="文档加载中"
        row-key="coze文档id"
        :row-selection="{
          selectedRowKeys: 选中的文档行,
          onChange: (selectedRowKeys) => { 选中的文档行.value = selectedRowKeys },
          getCheckboxProps: (record) => ({
            disabled: !record.coze文档id, // 没有coze文档id的不能选择
          }),
        }"
        @change="处理文档表格变化"
        class="文档表格"
      >
        <template #bodyCell="{ column, record }">
          <!-- 文档名称列 -->
          <template v-if="column.key === '文档名称'">
            <div class="文档名称显示">
              <file-text-outlined />
              <span>{{ record.文档名称 }}</span>
            </div>
          </template>

          <!-- 上传状态列 -->
          <template v-if="column.key === '上传状态'">
            <a-tag :color="获取上传状态颜色(record.上传状态)">
              {{ record.上传状态 || '未知' }}
            </a-tag>
          </template>

          <!-- 上传进度列 -->
          <template v-if="column.key === '上传进度'">
            <div style="width: 100px;">
              <a-progress
                :percent="record.上传进度 || 0"
                size="small"
                :status="获取进度条状态(record.上传状态)"
                :show-info="true"
              />
            </div>
          </template>

          <!-- 文件大小列 -->
          <template v-if="column.key === '文件大小'">
            {{ 格式化文件大小(record.文件大小 || 0) }}
          </template>

          <!-- 文件类型列 -->
          <template v-if="column.key === '文件类型'">
            <a-tag v-if="record.文件类型" color="blue">
              {{ record.文件类型 }}
            </a-tag>
            <span v-else>-</span>
          </template>

          <!-- 创建时间列 -->
          <template v-if="column.key === '创建时间'">
            {{ 格式化时间(record.创建时间) }}
          </template>

          <!-- 状态更新时间列 -->
          <template v-if="column.key === '状态更新时间'">
            {{ 格式化时间(record.状态更新时间) }}
          </template>

          <template v-if="column.key === 'content'">
            <div class="content-preview">
              {{ getContentPreview(record.内容) }}
            </div>
          </template>

          <!-- 操作列 -->
          <template v-if="column.key === '操作'">
            <a-space>
              <a-button size="small" @click="查看文档(record)">
                <eye-outlined />
                查看
              </a-button>
              <a-button size="small" @click="编辑文档(record)">
                <edit-outlined />
                编辑
              </a-button>
              <a-popconfirm
                title="确定要删除这个文档吗？删除后无法恢复。"
                @confirm="删除文档(record)"
                ok-text="确定"
                cancel-text="取消"
              >
                <a-button size="small" danger>
                  <delete-outlined />
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 空状态 -->
    <a-card v-else class="empty-state-card">
      <a-empty description="请先选择一个知识库">
        <template #image>
          <database-outlined style="font-size: 64px; color: #d9d9d9;" />
        </template>
      </a-empty>
    </a-card>

    <!-- 文档查看/编辑模态框 -->
    <a-modal
      v-model:open="文档弹窗可见"
      :title="文档弹窗标题"
      width="80%"
      :footer="文档弹窗模式 === 'view' ? null : undefined"
      @ok="处理文档保存"
      @cancel="处理文档弹窗取消"
    >
      <!-- 查看模式 -->
      <div v-if="文档弹窗模式 === 'view'" class="文档查看器">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="文档名称">
            {{ 当前文档?.文档名称 }}
          </a-descriptions-item>
          <a-descriptions-item label="上传状态">
            <a-tag :color="获取上传状态颜色(当前文档?.上传状态)">
              {{ 当前文档?.上传状态 }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="产品ID">
            {{ 当前文档?.产品id || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ 格式化时间(当前文档?.创建时间) }}
          </a-descriptions-item>
          <a-descriptions-item label="更新时间">
            {{ 格式化时间(当前文档?.更新时间) }}
          </a-descriptions-item>
          <a-descriptions-item label="文件大小">
            {{ 格式化文件大小(当前文档?.文件大小) }}
          </a-descriptions-item>
          <a-descriptions-item label="文件类型">
            {{ 当前文档?.文件类型 || '-' }}
          </a-descriptions-item>
        </a-descriptions>

        <div class="文档内容区域">
          <h4>状态描述</h4>
          <div class="文档内容">
            {{ 当前文档?.状态描述 || '暂无描述' }}
          </div>
        </div>
      </div>

      <!-- 编辑/添加模式 -->
      <div v-else class="文档编辑器">
        <a-form :model="文档表单" layout="vertical">
          <a-form-item label="文档名称" name="文档名称" :rules="[{ required: true, message: '请输入文档名称' }]">
            <a-input v-model:value="文档表单.文档名称" placeholder="请输入文档名称" />
          </a-form-item>
          <a-form-item label="文档内容" name="文档内容" :rules="[{ required: true, message: '请输入文档内容' }]">
            <a-textarea
              v-model:value="文档表单.文档内容"
              placeholder="请输入文档内容"
              :rows="15"
              show-count
              :maxlength="10000"
            />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  DatabaseOutlined,
  ReloadOutlined,
  PlusOutlined,
  FileTextOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  ClearOutlined
} from '@ant-design/icons-vue'
import knowledgeBaseService from '@/services/knowledgeBaseService'

// 响应式数据 - 使用中文变量名
const 知识库加载中 = ref(false)
const 文档加载中 = ref(false)
const 知识库列表 = ref([])
const 选中的知识id = ref(null)
const 选中的知识库 = ref(null)
const 文档列表 = ref([])
const 过滤后的文档列表 = ref([])
const 文档弹窗可见 = ref(false)
const 文档弹窗模式 = ref('view') // 'view' | 'edit' | 'add'
const 当前文档 = ref(null)
const 选中的文档行 = ref([]) // 选中的文档行keys

// 搜索和筛选
const 搜索关键词 = ref('')
const 状态筛选 = ref(null)
const 类型筛选 = ref(null)

// 文档统计
const 文档统计 = reactive({
  总数: 0,
  产品文档数: 0,
  自定义文档数: 0,
  最近更新: '-'
})

// 文档表单
const 文档表单 = reactive({
  文档名称: '',
  文档内容: ''
})

// 分页配置
const 文档分页配置 = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

// 文档表格列定义 - 直接使用后端中文键名
const 文档表格列配置 = [
  {
    title: '文档名称',
    key: '文档名称',
    dataIndex: '文档名称',
    width: 200
  },
  {
    title: '上传状态',
    key: '上传状态',
    dataIndex: '上传状态',
    width: 120
  },
  {
    title: '上传进度',
    key: '上传进度',
    dataIndex: '上传进度',
    width: 120
  },
  {
    title: '文件大小',
    key: '文件大小',
    dataIndex: '文件大小',
    width: 100
  },
  {
    title: '文件类型',
    key: '文件类型',
    dataIndex: '文件类型',
    width: 100
  },
  {
    title: '创建时间',
    key: '创建时间',
    dataIndex: '创建时间',
    width: 150
  },
  {
    title: '状态更新时间',
    key: '状态更新时间',
    dataIndex: '状态更新时间',
    width: 150
  },
  {
    title: '操作',
    key: '操作',
    width: 200,
    fixed: 'right'
  }
]

// 计算属性
const 文档弹窗标题 = computed(() => {
  switch (文档弹窗模式.value) {
    case 'view':
      return '查看文档'
    case 'edit':
      return '编辑文档'
    case 'add':
      return '添加文档'
    default:
      return '文档'
  }
})

// 工具方法
const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

const getStatusColor = (status) => {
  const statusMap = {
    '正常': 'green',
    '处理中': 'blue',
    '错误': 'red',
    '已删除': 'default'
  }
  return statusMap[status] || 'default'
}

const getSourceColor = (source) => {
  const sourceMap = {
    '产品文档': 'orange',
    '自定义文档': 'purple',
    '系统文档': 'cyan'
  }
  return sourceMap[source] || 'default'
}

const getContentPreview = (content) => {
  if (!content) return '暂无内容'
  return content.length > 50 ? content.substring(0, 50) + '...' : content
}

// 状态和格式化方法 - 使用中文方法名
const 获取上传状态颜色 = (状态) => {
  const 状态映射 = {
    '处理中': 'processing',
    '处理完成': 'success',
    '处理失败': 'error',
    '未知': 'default'
  }
  return 状态映射[状态] || 'default'
}

const 获取进度条状态 = (上传状态) => {
  const 状态映射 = {
    '处理中': 'active',
    '处理完成': 'success',
    '处理失败': 'exception',
    '未知': 'normal'
  }
  return 状态映射[上传状态] || 'normal'
}

const 格式化文件大小 = (字节数) => {
  if (!字节数 || 字节数 === 0) return '0 B'
  const k = 1024
  const 单位 = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(字节数) / Math.log(k))
  return parseFloat((字节数 / Math.pow(k, i)).toFixed(2)) + ' ' + 单位[i]
}

const 格式化时间 = (时间字符串) => {
  if (!时间字符串) return '-'
  return new Date(时间字符串).toLocaleString('zh-CN')
}

// 移除了获取来源颜色方法，因为不再显示来源标签

// 业务方法
const 刷新知识库列表 = async () => {
  知识库加载中.value = true
  try {
    console.log('🔄 开始获取知识库列表...')
    const 响应 = await knowledgeBaseService.获取知识库列表()

    if (响应.status === 100 && 响应.data) {
      知识库列表.value = 响应.data.知识库列表 || []
      console.log('✅ 获取知识库列表成功:', 知识库列表.value)
      message.success('知识库列表刷新成功')
    } else {
      console.error('❌ 获取知识库列表失败:', 响应)
      message.error(响应.message || '获取知识库列表失败')
    }
  } catch (error) {
    console.error('💥 刷新知识库列表异常:', error)
    message.error('刷新知识库列表失败')
  } finally {
    知识库加载中.value = false
  }
}

const 处理知识库变化 = (值) => {
  选中的知识库.value = 知识库列表.value.find(kb => kb.id === 值)
  if (选中的知识库.value) {
    刷新文档列表()
  }
}

const 刷新文档列表 = async () => {
  if (!选中的知识库.value) return

  文档加载中.value = true
  try {
    console.log('🔄 开始获取知识库文档列表...', 选中的知识库.value.id)

    const 响应 = await knowledgeBaseService.获取知识库文档列表({
      知识库表id: 选中的知识库.value.id,
      页码: 文档分页配置.current,
      每页数量: 文档分页配置.pageSize,
      同步状态: true // 同步Coze状态
    })

    if (响应.status === 100 && 响应.data) {
      文档列表.value = 响应.data.文档列表 || []
      文档分页配置.total = 响应.data.总数 || 0

      console.log('✅ 获取知识库文档列表成功:', 文档列表.value)
      console.log('📊 知识库信息:', 响应.data.知识库信息)

      // 更新统计信息
      更新文档统计()

      // 应用筛选
      应用筛选条件()

      message.success('文档列表刷新成功')
    } else {
      console.error('❌ 获取知识库文档列表失败:', 响应)
      message.error(响应.message || '获取文档列表失败')
    }
  } catch (error) {
    console.error('💥 刷新文档列表异常:', error)
    message.error('刷新文档列表失败')
  } finally {
    文档加载中.value = false
  }
}

const 更新文档统计 = () => {
  文档统计.总数 = 文档列表.value.length
  文档统计.产品文档数 = 文档列表.value.filter(doc => doc.产品id).length
  文档统计.自定义文档数 = 文档列表.value.filter(doc => !doc.产品id).length

  const 最新文档 = 文档列表.value.reduce((latest, doc) => {
    return new Date(doc.更新时间) > new Date(latest.更新时间) ? doc : latest
  }, 文档列表.value[0])

  文档统计.最近更新 = 最新文档 ? 格式化时间(最新文档.更新时间) : '-'
}

const 应用筛选条件 = () => {
  let 筛选后的列表 = [...文档列表.value]

  // 关键词搜索
  if (搜索关键词.value) {
    const 关键词 = 搜索关键词.value.toLowerCase()
    筛选后的列表 = 筛选后的列表.filter(doc =>
      doc.名称.toLowerCase().includes(关键词) ||
      doc.内容.toLowerCase().includes(关键词)
    )
  }

  // 状态筛选
  if (状态筛选.value) {
    筛选后的列表 = 筛选后的列表.filter(doc => doc.上传状态 === 状态筛选.value)
  }

  // 类型筛选
  if (类型筛选.value) {
    筛选后的列表 = 筛选后的列表.filter(doc => doc.文件类型 === 类型筛选.value)
  }

  过滤后的文档列表.value = 筛选后的列表
  文档分页配置.total = 筛选后的列表.length
}

const 处理搜索 = () => {
  应用筛选条件()
}

const 重置筛选条件 = () => {
  搜索关键词.value = ''
  状态筛选.value = null
  类型筛选.value = null
  应用筛选条件()
}

// 这些方法已经在前面定义过了，这里是重复的，删除这个重复定义

const 删除文档 = async (文档) => {
  try {
    console.log('🗑️ 开始删除文档:', 文档.文档名称)

    // 显示确认对话框
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除文档"${文档.文档名称}"吗？此操作不可撤销。`,
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        // 显示删除进度
        const hideLoading = message.loading('正在删除文档...', 0)

        try {
          const 响应 = await knowledgeBaseService.删除知识库文档({
            知识库表id: 选中的知识库.value.id,
            文档id列表: [文档.coze文档id]
          })

          console.log('📥 删除响应:', 响应)

          if (响应.status === 100) {
            const 删除数量 = 响应.data?.删除数量 || 1
            const 数据库影响行数 = 响应.data?.数据库影响行数 || 0

            message.success(`文档删除成功！删除了${删除数量}个文档，更新了${数据库影响行数}条数据库记录`)

            // 刷新文档列表
            await 刷新文档列表()

            console.log('✅ 文档删除完成')
          } else {
            const 错误信息 = 响应.message || 响应.data?.错误 || '删除文档失败'
            console.error('❌ 删除文档失败:', 响应)
            message.error(错误信息)

            // 如果有部分成功的情况，也刷新列表
            if (响应.data?.删除数量 > 0) {
              await 刷新文档列表()
            }
          }
        } catch (error) {
          console.error('💥 删除文档异常:', error)
          message.error('删除文档失败，请稍后重试')
        } finally {
          hideLoading()
        }
      },
      onCancel: () => {
        console.log('用户取消删除文档')
      }
    })
  } catch (error) {
    console.error('💥 准备删除文档异常:', error)
    message.error('操作失败，请稍后重试')
  }
}

const 查看文档 = (文档) => {
  当前文档.value = 文档
  文档弹窗模式.value = 'view'
  文档弹窗可见.value = true
}

const 编辑文档 = (文档) => {
  当前文档.value = 文档
  文档弹窗模式.value = 'edit'
  文档弹窗可见.value = true
  // 填充表单
  文档表单.文档名称 = 文档.文档名称
  文档表单.文档内容 = 文档.内容 || ''
}

const 显示添加文档弹窗 = () => {
  当前文档.value = null
  文档表单.文档名称 = ''
  文档表单.文档内容 = ''
  文档弹窗模式.value = 'add'
  文档弹窗可见.value = true
}

/**
 * 批量删除选中的文档
 */
const 批量删除文档 = async () => {
  if (!选中的文档行.value || 选中的文档行.value.length === 0) {
    message.warning('请先选择要删除的文档')
    return
  }

  try {
    const 选中数量 = 选中的文档行.value.length
    const 文档名称列表 = 选中的文档行.value.map(key => {
      const 文档 = 文档列表.value.find(doc => doc.coze文档id === key)
      return 文档?.文档名称 || key
    }).join('、')

    console.log('🗑️ 准备批量删除文档:', 选中的文档行.value)

    // 显示确认对话框
    Modal.confirm({
      title: '批量删除确认',
      content: `确定要删除以下${选中数量}个文档吗？\n\n${文档名称列表}\n\n此操作不可撤销。`,
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      width: 500,
      onOk: async () => {
        // 显示删除进度
        const hideLoading = message.loading(`正在删除${选中数量}个文档...`, 0)

        try {
          const 响应 = await knowledgeBaseService.删除知识库文档({
            知识库表id: 选中的知识库.value.id,
            文档id列表: 选中的文档行.value
          })

          console.log('📥 批量删除响应:', 响应)

          if (响应.status === 100) {
            const 删除数量 = 响应.data?.删除数量 || 选中数量
            const 数据库影响行数 = 响应.data?.数据库影响行数 || 0

            message.success(`批量删除成功！删除了${删除数量}个文档，更新了${数据库影响行数}条数据库记录`)

            // 清空选中状态
            选中的文档行.value = []

            // 刷新文档列表
            await 刷新文档列表()

            console.log('✅ 批量删除完成')
          } else {
            const 错误信息 = 响应.message || 响应.data?.错误 || '批量删除失败'
            console.error('❌ 批量删除失败:', 响应)

            // 检查是否有部分成功
            const 成功数量 = 响应.data?.删除数量 || 0
            if (成功数量 > 0) {
              message.warning(`部分删除成功：成功删除${成功数量}个文档，但有${选中数量 - 成功数量}个文档删除失败`)
              // 清空选中状态并刷新列表
              选中的文档行.value = []
              await 刷新文档列表()
            } else {
              message.error(错误信息)
            }
          }
        } catch (error) {
          console.error('💥 批量删除异常:', error)
          message.error('批量删除失败，请稍后重试')
        } finally {
          hideLoading()
        }
      },
      onCancel: () => {
        console.log('用户取消批量删除')
      }
    })
  } catch (error) {
    console.error('💥 准备批量删除异常:', error)
    message.error('操作失败，请稍后重试')
  }
}

const 处理文档保存 = async () => {
  try {
    if (文档弹窗模式.value === 'edit') {
      // TODO: 调用API更新文档
      message.success('文档更新成功')
    } else if (文档弹窗模式.value === 'add') {
      // TODO: 调用API添加文档
      message.success('文档添加成功')
    }

    文档弹窗可见.value = false
    刷新文档列表()
  } catch (error) {
    console.error('保存文档失败:', error)
    message.error('保存文档失败')
  }
}

const 处理文档弹窗取消 = () => {
  文档弹窗可见.value = false
  当前文档.value = null
}

const 处理文档表格变化 = (分页信息) => {
  文档分页配置.current = 分页信息.current
  文档分页配置.pageSize = 分页信息.pageSize
  刷新文档列表()
}

// 监听搜索和筛选变化
watch([搜索关键词, 状态筛选, 类型筛选], () => {
  应用筛选条件()
})

// 组件挂载时初始化
onMounted(() => {
  刷新知识库列表()
})
</script>

<style scoped>
.knowledge-management-module {
  padding: 0;
}

.intro-alert {
  margin-bottom: 24px;
}

.kb-selector-card,
.document-list-card,
.empty-state-card {
  margin-bottom: 24px;
}

.kb-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.kb-desc {
  color: #8c8c8c;
  font-size: 12px;
  margin-left: auto;
}

.stats-section {
  margin-bottom: 16px;
}

.search-section {
  margin-bottom: 16px;
}

.document-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.content-preview {
  color: #666;
  font-size: 12px;
  line-height: 1.4;
}

.document-viewer {
  max-height: 70vh;
  overflow-y: auto;
}

.document-content-section {
  margin-top: 24px;
}

.document-content-section h4 {
  margin-bottom: 12px;
  color: #262626;
}

.document-content {
  background: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  white-space: pre-wrap;
  line-height: 1.6;
  max-height: 400px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
}

.document-editor {
  max-height: 70vh;
  overflow-y: auto;
}

.document-table {
  margin-top: 16px;
}

.empty-state-card {
  text-align: center;
  padding: 40px 0;
}
</style>
