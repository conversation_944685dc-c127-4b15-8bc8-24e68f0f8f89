import asyncio
import time
import traceback  # 需要导入 traceback

from fastapi import Request, HTTPException, status  # 确保导入 status

from 日志 import 异步数据库记录接口日志, 记录安全事件, 错误日志器  # 假设这些日志工具可以全局导入
from 服务.异步用户服务 import 异步认证服务  # 导入认证服务


async def 日志中间件(request: Request, call_next):
    """全局请求日志中间件"""
    开始时间 = time.time()
    用户id = None
    response = None
    错误详情 = None
    堆栈跟踪 = None
    安全请求体 = ''  # 初始化为空字符串
    请求体 = None

    try:
        # 使用更安全的方式读取请求体
        try:
            请求体_字节 = await request.body()
            请求体 = 请求体_字节.decode('utf-8', errors='replace') if 请求体_字节 else ''
            # 截断长请求体
            安全请求体 = 请求体[:2048] if 请求体 else ''
            
            # 创建新的接收函数来避免请求体被消耗
            async def receive():
                return {"type": "http.request", "body": 请求体_字节}
            
            request._receive = receive
        except Exception as e:
            print(f"读取请求体错误: {str(e)}") # 此处print后续会统一处理为日志记录
            安全请求体 = f"[无法读取请求体: {str(e)}]"
        
        response = await call_next(request)

        # 提取用户身份信息
        try:
            auth_header = request.headers.get("authorization") or request.cookies.get("access_token")
            if auth_header:
                token = auth_header.split(" ")[-1]
                # 使用异步认证服务验证令牌
                认证服务实例 = 异步认证服务() # 创建实例
                用户数据 = await 认证服务实例.验证令牌(token)
                if 用户数据["status"] != 0:
                     # 根据项目规范，如果令牌验证失败（例如过期或无效），
                     # 仍应返回200，但响应体中包含错误状态和信息。
                     # 这里直接返回JSONResponse可能更符合FastAPI中间件的模式，但需要确认项目规范中对中间件内认证失败的处理方式。
                     # 暂时维持原逻辑，如果用户数据包含错误状态，让后续的路由处理或全局异常处理器处理。
                     # 但更标准的做法可能是在这里直接构造一个JSONResponse并返回。
                     # 考虑到这里仅仅是提取用户id用于日志，如果验证失败，用户id为None是可接受的。
                     pass # 让请求继续，用户id为None
                else:
                    用户id = 用户数据["data"]["用户信息"]["id"]
        except Exception as auth_error:
            print(f"提取用户信息失败: {str(auth_error)}") # 此处print后续会统一处理为日志记录
            # 继续处理，不影响响应

    except HTTPException as http_exc:
        错误详情 = str(http_exc.detail)
        # response = JSONResponse( # HTTPException 通常由FastAPI框架自动处理并生成响应
        #     status_code=http_exc.status_code,
        #     content=http_exc.detail
        # )
        raise http_exc # 重新抛出，让FastAPI的异常处理机制处理

    except Exception as e:
        错误详情 = f"{type(e).__name__}: {str(e)}"
        堆栈跟踪 = traceback.format_exc()
        print(f"请求处理异常: {错误详情}") # 此处print后续会统一处理为日志记录
        print(堆栈跟踪) # 此处print后续会统一处理为日志记录
        # response = JSONResponse( # 未知异常也应由全局异常处理器生成标准格式的响应
        #     status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        #     detail={"status": status.HTTP_500_INTERNAL_SERVER_ERROR, "message": f"系统异常: {str(e)}"}
        # )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"status": status.HTTP_500_INTERNAL_SERVER_ERROR, "message": f"系统异常: {str(e)}"}
        ) # 转换为HTTPException，以便全局异常处理器捕获

    finally:
        耗时 = time.time() - 开始时间
        状态码 = response.status_code if response else 500
        
        # 不记录健康检查请求日志
        if request.url.path not in ["/server-status", "/health"]:
            try:
                # 过滤敏感信息
                安全请求头 = str({k: v for k, v in request.headers.items() 
                           if k.lower() not in ["authorization", "cookie"]})
                
                日志数据 = {
                    "用户id": 用户id,
                    "ip": request.client.host or "unknown",
                    "路径": request.url.path,
                    "方法": request.method,
                    "状态码": 状态码,
                    "耗时": round(耗时, 3),
                    "请求头": 安全请求头,
                    "请求参数": str(request.query_params),
                    "请求体": 安全请求体,
                    "错误详情": 错误详情,
                    "堆栈跟踪": 堆栈跟踪
                }
                
                # 使用新的日志模块记录接口调用
                asyncio.create_task(异步数据库记录接口日志(**日志数据))
                
                # 记录安全相关事件
                if 状态码 in [401, 403, 429] or (状态码 >= 500 and 错误详情):
                    记录安全事件(
                        事件类型="API访问异常", 
                        用户id=用户id, 
                        事件描述=f"访问路径:{request.url.path}, 状态码:{状态码}, 错误:{错误详情}",
                        IP地址=request.client.host or "unknown",
                        成功=False
                    )
                
            except Exception as log_error:
                print(f"记录请求日志失败: {str(log_error)}") # 此处print后续会统一处理为日志记录
                错误日志器.error(f"记录请求日志失败: {str(log_error)}", exc_info=True) # 添加到错误日志
                
        # finally块必须返回response，即使它是None（例如在call_next之前发生异常）
        # 但如果call_next成功执行，response就应该是有效的
        # 如果在call_next之前或之中发生未被捕获并转换为HTTPException的错误，
        # 且没有raise，那么response可能是None，这会导致FastAPI出错。
        # 此处原代码的逻辑是，如果发生异常，会raise HTTPException，所以response不会是None。
        # 如果response是None，说明在call_next之前发生了未被捕获的异常，这是个问题。
        # 然而，FastAPI中间件的典型模式是，如果中间件不自行生成响应，它应该总是返回 await call_next(request)的结果
        # 这里的 finally 块是在 call_next 之后执行，所以 response 通常是有效的。 
        # 如果在 call_next 中发生异常并被捕获，raise 会中断执行，不会到 finally 中的 return。
        # 如果 call_next 正常但后续 finally 块中发生异常，这个 return 就不会执行。
        # FastAPI期望中间件要么返回一个Response对象，要么将异常传递出去。
        # 此处的日志中间件主要是记录，不应该改变响应流程，除非是为了处理认证失败（但目前逻辑不是这样）
        if response is None and not 错误详情: # 仅在没有错误且response意外为None时记录一个警告
            错误日志器.warning(f"日志中间件的finally块中response为None，但没有记录错误。路径: {request.url.path}")
        
        return response # 必须返回response 