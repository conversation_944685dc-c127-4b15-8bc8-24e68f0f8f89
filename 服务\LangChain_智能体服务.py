"""
LangChain智能体服务 - 统一的智能体管理服务

功能：
1. 智能体配置管理（创建、更新、删除、查询）
2. 智能体运行时管理（实例创建、对话、状态监控）
3. 用户智能体关联管理（分配、取消分配）
4. 智能体统计和监控
5. 智能体生命周期管理
"""

import asyncio
import json
import logging
import time
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

# PostgreSQL数据操作导入
# 保留部分MySQL数据层（暂时兼容，后续逐步迁移）
from 数据.LangChain_工具数据层 import LangChain工具数据层实例
from 数据.LangChain_智能体数据层 import LangChain智能体数据层, LangChain智能体数据层实例
from 数据.LangChain_模型数据层 import LangChain模型数据层实例
from 日志 import 应用日志器 as 智能体服务日志器
from 日志 import 错误日志器
from 服务.LangChain_RAG引擎 import RAG引擎实例
from 服务.LangChain_内部函数包装器 import 内部函数包装器实例
from 服务.LangChain_工具管理器 import LangChain工具管理器实例
from 服务.LangChain_文档处理器 import LangChain文档处理器实例
from 服务.LangChain_智能体常量 import 智能体状态
from 服务.LangChain_智能体错误处理器 import 智能体错误处理器

# 服务层导入
from 服务.LangChain_模型管理器 import LangChain模型管理器实例
from 服务.LangChain_知识库服务 import LangChain知识库服务实例
from 服务.LangChain_结构化输出处理器 import LangChain结构化输出处理器实例
from 服务.LangChain_记忆管理器 import LangChain记忆管理器实例
from 状态 import 状态


# 导入LangChain组件 - 参考Context7最佳实践
class LangChainComponents:
    """LangChain组件管理器 - 统一管理所有LangChain相关组件"""

    def __init__(self):
        self.available = False
        self._components = {}
        self._initialize_components()

    def _initialize_components(self):
        """初始化LangChain组件"""
        try:
            # 核心组件
            from langchain.agents import AgentExecutor
            from langchain.schema.runnable import Runnable
            from langchain_core.output_parsers import JsonOutputParser, StrOutputParser
            from langchain_core.prompts import ChatPromptTemplate
            from langchain_core.runnables import (
                RunnableLambda,
                RunnableParallel,
                RunnablePassthrough,
            )

            # 存储组件
            self._components.update(
                {
                    "AgentExecutor": AgentExecutor,
                    "Runnable": Runnable,
                    "JsonOutputParser": JsonOutputParser,
                    "StrOutputParser": StrOutputParser,
                    "ChatPromptTemplate": ChatPromptTemplate,
                    "RunnableLambda": RunnableLambda,
                    "RunnableParallel": RunnableParallel,
                    "RunnablePassthrough": RunnablePassthrough,
                }
            )

            self.available = True
            智能体服务日志器.info("✅ LangChain组件初始化成功")

        except ImportError as e:
            self.available = False
            智能体服务日志器.warning(f"⚠️ LangChain库不可用: {e}")

    def get_component(self, name: str):
        """获取组件"""
        if not self.available:
            return None
        return self._components.get(name)

    def create_str_parser(self):
        """创建字符串解析器"""
        parser_class = self.get_component("StrOutputParser")
        return parser_class() if parser_class else None

    def create_json_parser(self):
        """创建JSON解析器"""
        parser_class = self.get_component("JsonOutputParser")
        return parser_class() if parser_class else None

    def create_runnable_parallel(self, config):
        """创建并行运行器"""
        parallel_class = self.get_component("RunnableParallel")
        return parallel_class(config) if parallel_class else None

    def create_runnable_lambda(self, func):
        """创建Lambda运行器"""
        lambda_class = self.get_component("RunnableLambda")
        return lambda_class(func) if lambda_class else None

    def create_runnable_passthrough(self):
        """创建透传运行器"""
        passthrough_class = self.get_component("RunnablePassthrough")
        return passthrough_class() if passthrough_class else None


# 全局组件管理器实例
langchain_components = LangChainComponents()
LANGCHAIN_AVAILABLE = langchain_components.available


# Context7函数调用优化器 - 简化版本
class Context7函数调用优化器:
    """Context7风格的函数调用优化器 - 专注于参数优化和响应标准化"""

    @staticmethod
    def 优化函数参数(函数类型: str, 原始参数: Dict[str, Any]) -> Dict[str, Any]:
        """根据函数类型优化参数 - Context7模式"""
        if 函数类型 == "retrieval":
            return {
                "query": 原始参数.get("查询文本", 原始参数.get("query", "")),
                "limit": 原始参数.get("最大检索数量", 原始参数.get("limit", 5)),
                "threshold": 原始参数.get("相似度阈值", 原始参数.get("threshold", 0.7)),
            }
        elif 函数类型 == "model":
            return {
                "messages": 原始参数.get("消息列表", 原始参数.get("messages", [])),
                "temperature": 原始参数.get(
                    "温度参数", 原始参数.get("temperature", 0.7)
                ),
                "max_tokens": 原始参数.get(
                    "最大令牌数", 原始参数.get("max_tokens", 4000)
                ),
            }
        else:
            return 原始参数

    @staticmethod
    def 标准化响应(函数名: str, 原始响应: Any) -> Dict[str, Any]:
        """标准化函数响应 - Context7格式"""
        if isinstance(原始响应, dict) and "success" in 原始响应:
            return 原始响应

        return {
            "success": True,
            "data": 原始响应,
            "function_name": 函数名,
            "response_type": type(原始响应).__name__,
            "timestamp": datetime.now().isoformat(),
        }


# Context7错误处理装饰器 - 减少重复代码
def context7_错误处理(默认返回值=None, 错误前缀="操作失败"):
    """Context7风格的错误处理装饰器"""

    def decorator(func):
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                智能体服务日志器.error(f"{错误前缀}: {str(e)}")
                if 默认返回值 is not None:
                    return 默认返回值
                elif "Dict" in str(func.__annotations__.get("return", "")):
                    return {"success": False, "error": f"{错误前缀}: {str(e)}"}
                else:
                    return None

        return wrapper

    return decorator


# 全局函数调用优化器实例
context7_函数优化器 = Context7函数调用优化器()


# {{ AURA-X: Delete - 移除智能体类型枚举，统一使用基础智能体. Approval: 寸止(ID:1732456800). }}


# 智能体状态枚举已移至 LangChain_智能体常量.py


@dataclass
class 智能体配置:
    """智能体配置数据类"""

    用户id: int
    智能体名称: str
    智能体id: Optional[int] = None  # 添加智能体id字段，用于工具可用性检查
    模型名称: str = "qwen-turbo"
    系统提示词: str = ""
    用户提示词: str = ""
    角色设定: str = ""
    行为规范: str = ""
    温度参数: float = 0.7
    最大令牌数: int = 4000
    记忆窗口大小: int = 10
    启用rag: bool = False
    工具列表: Optional[List[str]] = None
    输出格式: str = "text"
    自定义回复格式: Optional[Dict[str, Any]] = None
    rag_配置: Optional[Dict[str, Any]] = None
    自定义变量: Optional[List[Dict[str, Any]]] = None

    def __post_init__(self):
        if self.工具列表 is None:
            self.工具列表 = []
        # 自定义回复格式现在直接是JSON Schema或None，不再是包装对象
        # 如果为None则保持None，不需要额外处理
        if self.rag_配置 is None:
            self.rag_配置 = {
                "检索策略": "similarity",
                "嵌入模型id": None,
                "相似度阈值": 0.7,
                "最大检索数量": 5,
            }
        if self.自定义变量 is None:
            self.自定义变量 = []


class 智能体实例:
    """智能体运行时实例"""

    def __init__(self, 配置: 智能体配置, 实例ID: str):
        self.配置 = 配置
        self.实例ID = 实例ID
        self.状态 = 智能体状态.初始化中
        self.创建时间 = datetime.now()
        self.最后活动时间 = datetime.now()
        # 存储用户id用于工具调用
        self.用户id = (
            getattr(配置, "用户id", None) or getattr(配置, "用户表id", None) or 1
        )

        # 核心组件 - 直接初始化避免None类型问题
        self.模型管理器 = LangChain模型管理器实例
        self.记忆管理器 = LangChain记忆管理器实例
        self.RAG引擎 = RAG引擎实例
        self.工具管理器 = LangChain工具管理器实例

        # 对话历史
        self.对话历史 = []

        # LangChain组件
        self.智能体执行器 = None  # 不使用类型注解避免导入错误
        self.对话链 = None  # 不使用类型注解避免导入错误

        # LCEL链组件 - 用于链式组合和并行执行
        self.基础对话链 = None  # 基础对话LCEL链
        self.RAG链 = None  # RAG增强LCEL链
        self.工具链 = None  # 工具调用LCEL链
        self.当前活动链 = None  # 当前使用的链

        # 提示词缓存 - 优化性能
        self._提示词缓存 = {}
        self._模板缓存 = {}

        # 自定义变量 - 用于临时存储对话级别的自定义变量
        self.自定义变量 = {}

        # JSON Schema配置 - 用于结构化输出
        self.json_schema = getattr(配置, "自定义回复格式", None)

        # 统计信息
        self.对话次数 = 0
        self.总令牌消耗 = 0
        self.错误次数 = 0

    async def 初始化(self) -> bool:
        """初始化智能体实例"""
        try:
            # 初始化服务组件
            await self._初始化服务组件()

            # 构建LangChain智能体
            await self._构建智能体()

            self.状态 = 智能体状态.运行中
            智能体服务日志器.info(f"智能体实例初始化成功: {self.实例ID}")
            return True

        except Exception as e:
            self.状态 = 智能体状态.错误
            错误响应 = 智能体错误处理器.处理通用错误(
                e, "智能体实例初始化", {"实例ID": self.实例ID}
            )
            智能体服务日志器.error(f"智能体实例初始化失败: {错误响应['error_message']}")
            return False

    async def _初始化服务组件(self):
        """初始化相关服务组件"""
        try:
            # 服务组件已在__init__中初始化，这里只需要确保模型管理器已初始化
            if not self.模型管理器.已初始化:
                await self.模型管理器.初始化()

            # 获取知识库服务
            self.知识库服务 = LangChain知识库服务实例

            # 获取提示词管理器（简化版本）
            try:
                # 暂时跳过提示词管理器，使用默认提示词
                智能体服务日志器.info("使用简化版本，跳过提示词管理器")
                self.提示词管理器 = None
            except Exception:
                智能体服务日志器.warning("提示词管理器不可用")
                self.提示词管理器 = None

            # 初始化RAG引擎（如果启用）
            if self.配置.启用rag:
                self.RAG引擎 = RAG引擎实例
                # 注意：这里需要传递数据库ID而不是字符串ID
                try:
                    智能体数据库ID = (
                        int(self.实例ID.split("_")[0])
                        if "_" in self.实例ID
                        else int(self.实例ID)
                    )
                    await self.RAG引擎.初始化(智能体id=智能体数据库ID)
                except ValueError:
                    智能体服务日志器.warning(
                        f"无法解析智能体id为整数: {self.实例ID}，跳过RAG初始化"
                    )
                    self.RAG引擎 = None
            else:
                self.RAG引擎 = None

            智能体服务日志器.info(f"服务组件初始化完成: {self.实例ID}")

        except Exception as e:
            智能体服务日志器.error(f"初始化服务组件失败: {str(e)}")
            raise

    async def _构建智能体(self):
        """构建LangChain智能体"""
        try:
            if not LANGCHAIN_AVAILABLE:
                智能体服务日志器.warning("LangChain不可用，使用简化模式")
                return

            # 获取模型（简化版本）
            if self.模型管理器 and hasattr(self.模型管理器, "获取模型"):
                模型 = await self.模型管理器.获取模型(self.配置.模型名称)
                if not 模型:
                    raise Exception(f"无法获取模型: {self.配置.模型名称}")
            else:
                智能体服务日志器.warning("模型管理器不可用，跳过模型获取")
                return

            # 构建提示词模板
            系统提示词 = self._构建系统提示词()

            # 构建统一的基础智能体（支持RAG和工具调用）
            await self._构建基础智能体(模型, 系统提示词)

            智能体服务日志器.info(f"LangChain智能体构建完成: {self.实例ID}")

        except Exception as e:
            智能体服务日志器.error(f"构建LangChain智能体失败: {str(e)}")
            raise

    def _构建系统提示词(self, 知识库上下文: str = "", 用户问题: str = "") -> str:
        """构建完整的系统提示词 - 使用LangChain最佳实践的结构化方法"""
        try:
            # 检查缓存 - 如果没有动态内容，可以使用缓存
            缓存键 = f"system_prompt_{hash(知识库上下文)}_{hash(用户问题)}"
            if 缓存键 in self._提示词缓存:
                智能体服务日志器.debug(f"🚀 使用缓存的系统提示词: {缓存键}")
                return self._提示词缓存[缓存键]

            # 使用LangChain的ChatPromptTemplate构建结构化提示词
            # from langchain_core.prompts import ChatPromptTemplate  # 已在顶部导入

            提示词组件 = []

            # 1. 角色设定 - 最高优先级，定义AI的基本身份
            if self.配置.角色设定:
                角色设定 = self._替换提示词变量(
                    self.配置.角色设定, 知识库上下文, 用户问题
                )
                提示词组件.append(f"🎭 角色身份：\n{角色设定}")

            # 2. 系统提示词 - 核心行为指导
            if self.配置.系统提示词:
                系统指导 = self._替换提示词变量(
                    self.配置.系统提示词, 知识库上下文, 用户问题
                )
                提示词组件.append(f"🤖 系统指导：\n{系统指导}")

            # 3. 行为规范 - 约束和边界
            if self.配置.行为规范:
                行为约束 = self._替换提示词变量(
                    self.配置.行为规范, 知识库上下文, 用户问题
                )
                提示词组件.append(f"📋 行为规范：\n{行为约束}")

            # 4. 知识库上下文 - 专业知识支撑
            if 知识库上下文:
                提示词组件.append(f"📚 知识库参考：\n{知识库上下文}")

            # 5. 结构化输出指令 - 格式要求（如果需要）
            if self.配置.输出格式 in ["json", "structured"]:
                # 检查是否有自定义JSON Schema - 新数据结构
                json_schema = None
                智能体服务日志器.info(
                    f"🔍 检查自定义回复格式: 类型={type(self.配置.自定义回复格式)}, 内容={self.配置.自定义回复格式}"
                )

                if (
                    self.配置.自定义回复格式
                    and "properties" in self.配置.自定义回复格式
                ):
                    json_schema = self.配置.自定义回复格式
                    智能体服务日志器.info(
                        f"✅ JSON Schema设置成功，properties数量: {len(json_schema.get('properties', {}))}"
                    )
                else:
                    智能体服务日志器.warning("⚠️ JSON Schema条件不满足")

                # 传入JSON Schema生成正确的指令和示例
                json_指令 = self._构建JSON输出指令(json_schema)

                # 如果有Schema，添加详细说明
                if json_schema:
                    模式说明 = self._构建JSON模式说明(json_schema)
                    完整指令 = f"{json_指令}\n\n{模式说明}"
                    提示词组件.append(f"📄 输出格式要求：\n{完整指令}")
                else:
                    提示词组件.append(f"📄 输出格式要求：\n{json_指令}")

            # 6. 质量保证提示 - 基于LangChain最佳实践
            质量要求 = self._构建质量保证提示()
            提示词组件.append(质量要求)

            # 构建最终的系统提示词
            最终提示词 = "\n\n".join(提示词组件)

            # 缓存结果（如果合适的话）
            if len(最终提示词) < 10000:  # 只缓存较小的提示词
                self._提示词缓存[缓存键] = 最终提示词
                # 限制缓存大小
                if len(self._提示词缓存) > 100:
                    # 移除最旧的缓存项
                    oldest_key = next(iter(self._提示词缓存))
                    del self._提示词缓存[oldest_key]

            # 记录提示词构建信息 - Context7优化的详细日志
            智能体服务日志器.info(
                f"🏗️ 系统提示词构建完成: {len(提示词组件)}个组件, 总长度: {len(最终提示词)}字符"
            )

            # 详细记录每个组件（调试模式）
            if 智能体服务日志器.isEnabledFor(logging.DEBUG):
                for i, 组件 in enumerate(提示词组件, 1):
                    组件预览 = 组件[:100] + "..." if len(组件) > 100 else 组件
                    智能体服务日志器.debug(f"📝 组件 {i}: {组件预览}")

            # 验证提示词质量
            if not 最终提示词.strip():
                智能体服务日志器.warning("⚠️ 构建的系统提示词为空，可能影响智能体行为")
            elif len(最终提示词) < 50:
                智能体服务日志器.warning("⚠️ 系统提示词过短，可能缺少必要的指导信息")

            智能体服务日志器.info("✅ 系统提示词验证通过，准备传递给模型")

            return 最终提示词

        except Exception as e:
            智能体服务日志器.error(f"❌ 构建系统提示词失败: {str(e)}")
            # 返回基础的系统提示词作为后备
            return self._构建基础系统提示词(知识库上下文, 用户问题)

    def _构建质量保证提示(self) -> str:
        """构建质量保证提示 - 基于LangChain最佳实践"""
        return """🔍 回复质量标准：
- 准确理解用户需求，提供针对性回答
- 基于提供的知识库内容，确保信息准确性
- 保持语言表达清晰自然，逻辑结构井然有序
- 如无法确定答案，诚实说明并提供可能的方向
- 遵循角色设定和行为规范，保持一致性"""

    def _构建基础系统提示词(self, 知识库上下文: str = "", 用户问题: str = "") -> str:
        """构建基础系统提示词 - 作为异常情况的后备方案，参考Context7最佳实践"""
        # 移除未使用的参数警告 - 保持接口一致性
        _ = 用户问题  # 标记参数已使用，避免类型检查警告
        基础提示 = f"你是{self.配置.智能体名称 or '智能助手'}，一个专业、友好的AI助手。"

        if 知识库上下文:
            基础提示 += f"\n\n📚 参考信息：\n{知识库上下文}"

        基础提示 += "\n\n请根据用户问题提供准确、有用的回答。"

        return 基础提示

    def _转义JSON内容(self, 内容: str) -> str:
        """统一的JSON内容转义方法，避免LangChain将JSON字段名误认为模板变量"""
        try:
            # 转义大括号，避免LangChain误认为是变量
            return 内容.replace("{", "{{").replace("}", "}}")
        except Exception as e:
            智能体服务日志器.warning(f"JSON转义失败: {str(e)}")
            return 内容

    async def _加载工具实例(
        self, 工具名称: str, 工具配置id: Optional[int] = None
    ) -> Optional[Any]:
        """统一的工具实例加载方法，消除重复逻辑"""
        try:
            # 1. 首先尝试从工具管理器获取
            if self.工具管理器 and 工具名称 in self.工具管理器.工具注册表:
                工具实例 = self.工具管理器.工具注册表[工具名称]
                智能体服务日志器.debug(f"✅ 从工具管理器加载: {工具名称}")
                return 工具实例

            # 2. 尝试从内部函数包装器获取（需要用户id）
            try:
                # 获取用户id
                用户id = (
                    getattr(self, "用户id", None)
                    or getattr(self.配置, "用户表id", None)
                    or 1
                )
                用户工具列表 = await 内部函数包装器实例.获取用户工具列表(用户id)
                if 工具名称 in 用户工具列表:
                    工具实例 = 用户工具列表[工具名称]
                    智能体服务日志器.debug(f"✅ 从内部函数包装器加载: {工具名称}")
                    return 工具实例
            except Exception as e:
                智能体服务日志器.debug(f"内部函数包装器加载失败: {e}")

            智能体服务日志器.warning(f"⚠️ 工具未找到: {工具名称}")
            return None

        except Exception as e:
            智能体服务日志器.error(f"加载工具实例失败 {工具名称}: {str(e)}")
            return None

    def _构建检索参数(self, 关联配置: Dict[str, Any]) -> Dict[str, Any]:
        """统一的检索参数构建方法 - 支持智能多层检索和查询优化"""
        基础参数 = {
            "最大检索数量": max(1, min(20, 关联配置.get("最大检索数量", 5))),
            "相似度阈值": max(0.0, min(1.0, 关联配置.get("相似度阈值", 0.7))),
            "检索策略": 关联配置.get("检索策略", "similarity"),
        }

        # 智能多层检索配置
        智能检索配置 = {
            "启用智能多层检索": True,  # 默认启用智能检索
            "最小结果数量": max(1, min(10, 关联配置.get("最小结果数量", 3))),
            "质量阈值": max(0.0, min(1.0, 关联配置.get("质量阈值", 0.6))),
            "启用自适应阈值": 关联配置.get("启用自适应阈值", True),
        }

        # 查询优化配置 - 从关联配置中获取（数据库中的配置）
        查询优化配置 = 关联配置.get("查询优化配置", {})
        if 查询优化配置:
            智能体服务日志器.debug(f"🔧 获取查询优化配置: {查询优化配置}")

        # 合并配置
        基础参数.update(智能检索配置)
        if 查询优化配置:
            基础参数["查询优化配置"] = 查询优化配置

        return 基础参数

    async def _检查是否需要智能回退(
        self, 检索结果: List[Dict], 用户输入: str, 检索参数: Dict
    ) -> bool:
        """检查是否需要启动智能回退机制"""
        try:
            if not 检索结果:
                return True  # 无结果时需要回退

            # 检查结果数量
            最小结果数量 = 检索参数.get("最小结果数量", 3)
            if len(检索结果) < 最小结果数量:
                智能体服务日志器.debug(
                    f"结果数量不足: {len(检索结果)} < {最小结果数量}"
                )
                return True

            # 检查结果质量
            平均相似度 = sum(结果.get("相似度", 0) for 结果 in 检索结果) / len(检索结果)
            质量阈值 = 检索参数.get("质量阈值", 0.6)

            if 平均相似度 < 质量阈值:
                智能体服务日志器.debug(
                    f"结果质量不足: 平均相似度 {平均相似度:.3f} < {质量阈值}"
                )
                return True

            # 检查内容相关性（简化版本）
            查询关键词 = set(用户输入.lower().split())
            查询关键词 = {词 for 词 in 查询关键词 if len(词) > 1}

            if 查询关键词:
                相关结果数 = 0
                for 结果 in 检索结果:
                    内容 = 结果.get("内容", "").lower()
                    if any(关键词 in 内容 for 关键词 in 查询关键词):
                        相关结果数 += 1

                相关性比例 = 相关结果数 / len(检索结果)
                if 相关性比例 < 0.5:  # 少于50%的结果相关
                    智能体服务日志器.debug(f"内容相关性不足: {相关性比例:.2%}")
                    return True

            return False  # 不需要回退

        except Exception as e:
            智能体服务日志器.warning(f"检查智能回退条件失败: {str(e)}")
            return False  # 出错时不回退

    async def _执行智能回退(
        self, 用户输入: str, 现有结果: List[Dict]
    ) -> Dict[str, Any]:
        """执行智能回退机制"""
        try:
            智能体服务日志器.info(f"🧠 执行智能回退: 用户输入='{用户输入[:50]}...'")

            回退策略 = []

            # 1. 对话历史分析
            历史上下文 = await self._分析对话历史(用户输入)
            if 历史上下文:
                回退策略.append({"类型": "对话历史", "内容": 历史上下文, "权重": 0.4})

            # 2. 查询扩展和重写
            扩展查询 = await self._生成查询扩展(用户输入)
            if 扩展查询:
                回退策略.append({"类型": "查询扩展", "内容": 扩展查询, "权重": 0.3})

            # 3. 通用知识补充
            通用知识 = await self._提供通用知识指导(用户输入)
            if 通用知识:
                回退策略.append({"类型": "通用知识", "内容": 通用知识, "权重": 0.3})

            if 回退策略:
                智能体服务日志器.info(
                    f"✅ 智能回退策略生成完成: {len(回退策略)} 个策略"
                )
                return {
                    "回退策略": 回退策略,
                    "现有结果数量": len(现有结果),
                    "回退原因": "检索结果不足或质量不达标",
                }
            else:
                return {}

        except Exception as e:
            智能体服务日志器.error(f"执行智能回退失败: {str(e)}")
            return {}

    async def _分析对话历史(self, 当前输入: str) -> str:
        """分析对话历史提供上下文"""
        try:
            # 简化版本：基于当前会话的历史消息
            if hasattr(self, "当前会话历史") and self.当前会话历史:
                最近消息 = self.当前会话历史[-3:]  # 取最近3条消息
                历史内容 = []

                for 消息 in 最近消息:
                    if isinstance(消息, dict):
                        角色 = 消息.get("role", "")
                        内容 = 消息.get("content", "")
                        if 角色 and 内容 and len(内容.strip()) > 0:
                            历史内容.append(f"{角色}: {内容[:100]}...")

                if 历史内容:
                    return "基于对话历史的上下文：\n" + "\n".join(历史内容)

            return ""

        except Exception as e:
            智能体服务日志器.warning(f"分析对话历史失败: {str(e)}")
            return ""

    async def _生成查询扩展(self, 原始查询: str) -> str:
        """生成查询扩展和重写"""
        try:
            # 简化版本：基于关键词扩展
            关键词 = 原始查询.split()
            扩展建议 = []

            # 添加同义词和相关词（简化版本）
            同义词映射 = {
                "问题": ["疑问", "困难", "挑战"],
                "方法": ["方式", "途径", "策略"],
                "解决": ["处理", "应对", "解答"],
                "如何": ["怎样", "怎么", "如何才能"],
                "什么": ["哪些", "什么样的"],
            }

            for 词 in 关键词:
                if 词 in 同义词映射:
                    扩展建议.extend(同义词映射[词])

            if 扩展建议:
                return (
                    f"查询扩展建议：可以尝试搜索 {', '.join(扩展建议[:3])} 等相关内容"
                )

            return ""

        except Exception as e:
            智能体服务日志器.warning(f"生成查询扩展失败: {str(e)}")
            return ""

    async def _提供通用知识指导(self, 用户输入: str) -> str:
        """提供通用知识指导"""
        try:
            # 简化版本：基于问题类型提供通用指导
            输入小写 = 用户输入.lower()

            通用指导模板 = {
                "如何": "这是一个操作性问题，建议您：\n1. 明确具体的操作目标\n2. 查看相关文档或教程\n3. 寻求专业人士的帮助",
                "什么": "这是一个概念性问题，建议您：\n1. 查阅权威资料或文档\n2. 了解基本定义和特征\n3. 结合实际案例理解",
                "为什么": "这是一个原理性问题，建议您：\n1. 了解背景和原因\n2. 分析相关因素\n3. 查看专业解释",
                "错误": "遇到错误时，建议您：\n1. 仔细查看错误信息\n2. 检查操作步骤\n3. 查阅故障排除指南",
                "问题": "解决问题的一般步骤：\n1. 明确问题的具体表现\n2. 分析可能的原因\n3. 尝试相应的解决方案",
            }

            for 关键词, 指导 in 通用指导模板.items():
                if 关键词 in 输入小写:
                    return f"通用指导：\n{指导}"

            # 默认通用指导
            return "通用建议：\n1. 尝试重新描述您的问题\n2. 提供更多具体信息\n3. 查看相关文档或寻求专业帮助"

        except Exception as e:
            智能体服务日志器.warning(f"提供通用知识指导失败: {str(e)}")
            return ""

    def _构建LangChain提示词模板(
        self, 知识库上下文: str = "", 用户问题: str = ""
    ) -> Optional[Any]:
        """构建LangChain标准的ChatPromptTemplate - 新的标准化方法"""
        try:
            if not LANGCHAIN_AVAILABLE:
                智能体服务日志器.warning("LangChain不可用，无法构建标准提示词模板")
                return None

            messages = []

            # 1. 构建系统消息
            系统消息内容 = self._构建系统提示词(知识库上下文, 用户问题)
            if 系统消息内容:
                messages.append(("system", 系统消息内容))

            # 2. 添加用户消息模板
            if self.配置.用户提示词:
                # 对用户提示词进行变量替换，但保留{input}占位符
                用户模板 = self._替换用户提示词变量(
                    self.配置.用户提示词, 知识库上下文, 用户问题
                )
                messages.append(("human", 用户模板))
            else:
                # 默认用户消息模板
                messages.append(("human", "{input}"))

            # 3. 构建ChatPromptTemplate
            ChatPromptTemplate = langchain_components.get_component(
                "ChatPromptTemplate"
            )
            if ChatPromptTemplate:
                提示词模板 = ChatPromptTemplate.from_messages(messages)
            else:
                提示词模板 = None

            智能体服务日志器.debug(
                f"🏗️ LangChain提示词模板构建成功: {len(messages)}个消息组件"
            )
            return 提示词模板

        except Exception as e:
            智能体服务日志器.error(f"❌ 构建LangChain提示词模板失败: {str(e)}")
            return None

    def _验证提示词模板(self, 模板: Any) -> Tuple[bool, str]:
        """验证提示词模板的有效性"""
        try:
            if not 模板:
                return False, "模板为空"

            # 检查模板是否有消息
            if not hasattr(模板, "messages") or not 模板.messages:
                return False, "模板没有消息组件"

            # 尝试格式化模板以验证语法
            try:
                测试变量 = {
                    "input": "测试输入",
                    "knowledge_context": "",
                    "user_question": "测试问题",
                }
                模板.format_messages(**测试变量)
                return True, "模板验证通过"
            except Exception as format_error:
                return False, f"模板格式化失败: {str(format_error)}"

        except Exception as e:
            return False, f"模板验证异常: {str(e)}"

    async def 重新加载提示词配置(self) -> bool:
        """重新加载提示词配置 - 用于配置更新后的热重载"""
        try:
            智能体服务日志器.info(f"🔄 开始重新加载提示词配置: {self.实例ID}")

            # 从数据库重新获取最新的智能体配置

            # 解析智能体id
            try:
                智能体数据库ID = (
                    int(self.实例ID.split("_")[0])
                    if "_" in self.实例ID
                    else int(self.实例ID)
                )
            except ValueError:
                智能体服务日志器.error(f"❌ 无法解析智能体id: {self.实例ID}")
                return False

            # 获取最新配置
            最新配置数据 = await LangChain智能体数据层实例.获取智能体详情完整(
                智能体数据库ID
            )
            if not 最新配置数据:
                智能体服务日志器.error(f"❌ 无法获取智能体配置: ID={智能体数据库ID}")
                return False

            # 更新配置对象的提示词相关字段 - 使用安全的字典访问
            if "系统提示词" in 最新配置数据:
                self.配置.系统提示词 = 最新配置数据["系统提示词"]
            if "用户提示词" in 最新配置数据:
                self.配置.用户提示词 = 最新配置数据["用户提示词"]
            if "角色设定" in 最新配置数据:
                self.配置.角色设定 = 最新配置数据["角色设定"]
            if "行为规范" in 最新配置数据:
                self.配置.行为规范 = 最新配置数据["行为规范"]

            # 重新构建LangChain提示词模板
            if LANGCHAIN_AVAILABLE:
                新模板 = self._构建LangChain提示词模板()
                if 新模板:
                    验证结果, 验证信息 = self._验证提示词模板(新模板)
                    if 验证结果:
                        智能体服务日志器.info(
                            f"✅ 提示词模板重新构建成功: {self.实例ID}"
                        )
                    else:
                        智能体服务日志器.warning(f"⚠️ 新提示词模板验证失败: {验证信息}")

            # 清除可能的缓存
            if hasattr(self, "_提示词缓存"):
                self._提示词缓存.clear()

            智能体服务日志器.info(f"✅ 提示词配置重新加载完成: {self.实例ID}")
            return True

        except Exception as e:
            智能体服务日志器.error(
                f"❌ 重新加载提示词配置失败: {self.实例ID}, 错误={str(e)}"
            )
            return False

    def _构建JSON模式说明(self, json_schema: Dict[str, Any]) -> str:
        """构建JSON Schema的详细说明"""
        说明部分 = ["🎯 JSON结构要求："]

        properties = json_schema.get("properties", {})
        required_fields = json_schema.get("required", [])

        if properties:
            说明部分.append("必须包含以下字段：")
            for 字段名, 字段信息 in properties.items():
                是否必需 = "✅" if 字段名 in required_fields else "🔶"
                字段类型 = 字段信息.get("type", "string")
                字段描述 = 字段信息.get("description", "")

                if 字段描述:
                    说明部分.append(f"  {是否必需} {字段名} ({字段类型}): {字段描述}")
                else:
                    说明部分.append(f"  {是否必需} {字段名} ({字段类型})")

        if json_schema.get("title"):
            说明部分.append(f"\n📝 结构名称: {json_schema['title']}")

        if json_schema.get("description"):
            说明部分.append(f"📖 结构说明: {json_schema['description']}")

        说明部分.append("\n⚠️ 严格按照以上结构输出，不得遗漏必需字段！")

        return "\n".join(说明部分)

    def _构建JSON输出指令(self, json_schema: Optional[Dict[str, Any]] = None) -> str:
        """构建JSON输出格式指令 - 根据实际JSON Schema生成正确示例"""
        智能体服务日志器.info(
            f"🔍 构建JSON输出指令，Schema是否存在: {json_schema is not None}"
        )
        if json_schema:
            智能体服务日志器.info(
                f"🔍 Schema properties: {list(json_schema.get('properties', {}).keys())}"
            )

        基础指令 = """📄 输出格式要求：

请严格按照JSON格式输出您的回复。输出必须是有效的JSON格式，不要包含任何额外的文本或解释。

⚠️ 重要：您的回复必须使用JSON格式，这是强制性要求。

基本格式要求：
- 使用双引号包围字符串
- 确保JSON语法正确
- 不要在JSON前后添加额外文本
- 必须输出标准的JSON对象格式

🎯 特别注意回复内容字段格式：
- "回复内容"字段必须是对象数组，不是字符串数组
- 每个数组元素都必须是包含"内容"和"类型"字段的对象
- 格式：[{{"内容": "具体消息内容", "类型": "文本"}}]
- 禁止使用：["文本消息1", "文本消息2"] 这种字符串数组格式"""

        # 如果有JSON Schema，生成对应的示例
        if json_schema and "properties" in json_schema:
            智能体服务日志器.info(
                f"🔍 开始生成示例数据，字段数量: {len(json_schema['properties'])}"
            )
            示例数据 = {}
            properties = json_schema["properties"]

            for 字段名, 字段信息 in properties.items():
                智能体服务日志器.info(
                    f"🔍 处理字段: {字段名}, 类型: {字段信息.get('type', 'unknown')}"
                )
                字段类型 = 字段信息.get("type", "string")
                默认值 = 字段信息.get("default")

                if 默认值 is not None:
                    示例数据[字段名] = 默认值
                elif 字段类型 == "string":
                    示例数据[字段名] = f"示例{字段名}内容"
                elif 字段类型 == "number":
                    示例数据[字段名] = 0.8
                elif 字段类型 == "integer":
                    示例数据[字段名] = 1
                elif 字段类型 == "boolean":
                    示例数据[字段名] = True
                elif 字段类型 == "array":
                    # 特殊处理"回复内容"字段
                    智能体服务日志器.info(f"🔍 处理数组字段: {字段名}")
                    if 字段名 == "回复内容":
                        智能体服务日志器.info("✅ 应用回复内容特殊处理")
                        示例数据[字段名] = [{"内容": "示例回复内容", "类型": "文本"}]
                    else:
                        # 检查数组元素类型
                        items_info = 字段信息.get("items", {})
                        items_type = items_info.get("type", "string")

                        if items_type == "object" and "properties" in items_info:
                            # 数组对象类型，生成对象示例
                            对象示例 = {}
                            items_properties = items_info["properties"]
                            # items_required = items_info.get("required", [])  # 暂时不使用

                            for 属性名, 属性信息 in items_properties.items():
                                属性类型 = 属性信息.get("type", "string")
                                if 属性类型 == "string":
                                    对象示例[属性名] = f"示例{属性名}内容"
                                elif 属性类型 == "integer":
                                    对象示例[属性名] = 1
                                elif 属性类型 == "number":
                                    对象示例[属性名] = 1.0
                                elif 属性类型 == "boolean":
                                    对象示例[属性名] = True
                                else:
                                    对象示例[属性名] = f"示例{属性名}"

                            示例数据[字段名] = [对象示例]
                        else:
                            # 基本类型数组
                            示例数据[字段名] = ["示例项1", "示例项2"]
                else:
                    示例数据[字段名] = f"示例{字段名}"

            import json

            智能体服务日志器.info(f"🔍 生成的示例数据: {示例数据}")
            示例JSON = json.dumps(示例数据, ensure_ascii=False, indent=2)
            智能体服务日志器.info(f"🔍 最终示例JSON: {示例JSON}")

            # 使用统一的JSON转义方法
            转义后的示例JSON = self._转义JSON内容(示例JSON)

            基础指令 += f"""

输出格式示例（必须是纯JSON）：
{转义后的示例JSON}

⚠️ 再次提醒：确保"回复内容"是对象数组格式，每个对象包含"内容"和"类型"字段！"""
        else:
            # 如果没有Schema，使用通用示例
            基础指令 += """

输出格式示例（必须是纯JSON）：
{{
  "后续建议": "示例后续建议内容",
  "回复内容": [
    {
      "内容": "示例回复内容",
      "类型": "文本"
    }
  ]
}

⚠️ 再次提醒：确保"回复内容"是对象数组格式，每个对象包含"内容"和"类型"字段！"""

        基础指令 += """

请确保您的所有回复都是有效的JSON格式。"""

        return 基础指令

    async def _构建基础智能体(self, 模型, 系统提示词: str):
        """构建统一的基础智能体 - 根据配置自动启用rag和工具调用功能"""
        try:
            if not LANGCHAIN_AVAILABLE:
                智能体服务日志器.warning("LangChain不可用，跳过智能体构建")
                return

            智能体服务日志器.info(f"🔧 开始构建基础智能体: {self.实例ID}")

            # 检查是否需要RAG功能 - 动态获取知识库列表
            智能体id = int(self.实例ID) if self.实例ID.isdigit() else None
            智能体服务日志器.info(
                f"🔧 [链构建调试] 智能体id解析: {智能体id}, 原始实例ID: {self.实例ID}"
            )

            知识库列表 = (
                await self._获取智能体关联知识库列表(智能体id) if 智能体id else []
            )
            需要RAG = self.配置.启用rag and len(知识库列表) > 0

            # 调试：输出RAG配置信息
            智能体服务日志器.info(
                f"🔧 [链构建调试] RAG配置检查: 启用rag={self.配置.启用rag} (类型:{type(self.配置.启用rag)}), 知识库列表={知识库列表}, 知识库数量={len(知识库列表)}, 需要RAG={需要RAG}"
            )

            # 检查是否需要工具调用功能
            需要工具 = await self._检查工具可用性()
            智能体服务日志器.info(
                f"🔧 [链构建调试] 工具可用性检查: 需要工具={需要工具}"
            )

            if 需要RAG and 需要工具:
                # RAG + 工具调用
                智能体服务日志器.info("🔧 [链构建调试] 选择构建: RAG + 工具调用智能体")
                await self._构建RAG工具智能体(模型, 系统提示词)
            elif 需要RAG:
                # 仅RAG
                智能体服务日志器.info("🔧 [链构建调试] 选择构建: 纯RAG智能体")
                await self._构建RAG智能体(模型, 系统提示词)
            elif 需要工具:
                # 仅工具调用
                智能体服务日志器.info("🔧 [链构建调试] 选择构建: 纯工具调用智能体")
                await self._构建工具智能体(模型, 系统提示词)
            else:
                # 基础对话
                智能体服务日志器.info("🔧 [链构建调试] 选择构建: 基础对话智能体")
                await self._构建基础对话智能体(模型, 系统提示词)

            智能体服务日志器.info(
                f"✅ 基础智能体构建完成: RAG={需要RAG}, 工具={需要工具}"
            )

        except Exception as e:
            智能体服务日志器.error(f"❌ 构建基础智能体失败: {str(e)}")
            # 降级到最简单的对话模式
            await self._构建基础对话智能体(模型, 系统提示词)

    async def _检查工具可用性(self) -> bool:
        """检查是否有可用的工具"""
        try:
            if not self.工具管理器:
                智能体服务日志器.debug("工具管理器不可用")
                return False

            # 检查数据库中的工具关联 - 修复智能体id获取
            数据库工具可用 = False
            智能体id = getattr(self.配置, "智能体id", None)  # 使用新添加的智能体id字段
            智能体服务日志器.debug(f"🔧 [工具可用性检查] 智能体id: {智能体id}")

            if 智能体id:
                try:
                    # 直接调用数据层获取工具关联
                    if not LangChain工具数据层实例.已初始化:
                        await LangChain工具数据层实例.初始化()

                    工具关联列表 = await LangChain工具数据层实例.获取智能体工具关联(
                        智能体id
                    )
                    启用工具数量 = sum(
                        1 for 关联 in 工具关联列表 if 关联.get("启用状态", True)
                    )
                    数据库工具可用 = 启用工具数量 > 0
                    智能体服务日志器.info(
                        f"🔧 [工具可用性检查] 智能体 {智能体id} 有 {启用工具数量} 个启用的工具"
                    )
                except Exception as db_e:
                    智能体服务日志器.warning(f"检查数据库工具关联失败: {db_e}")

            # 检查内部函数工具
            内部工具可用 = False
            用户id = getattr(self.配置, "用户id", None)  # 使用标准的用户id字段
            if 用户id and isinstance(用户id, int) and 用户id > 0:
                try:
                    # 确保内部函数包装器已初始化
                    if not 内部函数包装器实例.已初始化:
                        await 内部函数包装器实例.初始化()

                    # 注意：获取用户工具列表不是async方法
                    可用内部工具 = 内部函数包装器实例.获取用户工具列表(用户id)
                    内部工具可用 = len(可用内部工具) > 0
                    智能体服务日志器.info(
                        f"🔧 [工具可用性检查] 用户 {用户id} 有 {len(可用内部工具)} 个内部工具"
                    )
                except Exception as e:
                    智能体服务日志器.warning(f"获取用户内部工具失败: {e}")
                    内部工具可用 = False

            # 任一类型工具可用即返回True
            工具可用 = 数据库工具可用 or 内部工具可用
            智能体服务日志器.info(f"🔧 [工具可用性检查] 最终结果: {工具可用}")
            return 工具可用

        except Exception as e:
            智能体服务日志器.warning(f"检查工具可用性失败: {e}")
            return False

    async def _构建RAG工具智能体(self, 模型, 系统提示词: str):
        """构建RAG+工具调用的复合智能体"""
        try:
            智能体服务日志器.info("🔧 构建RAG+工具复合智能体")

            # 优先尝试工具调用模式（包含RAG检索）
            await self._构建工具智能体(模型, 系统提示词)

            # 如果工具构建失败，降级到RAG模式
            if not self.当前活动链:
                await self._构建RAG智能体(模型, 系统提示词)

        except Exception as e:
            智能体服务日志器.error(f"构建RAG+工具智能体失败: {e}")
            # 最终降级到基础对话
            await self._构建基础对话智能体(模型, 系统提示词)

    async def _构建基础对话智能体(self, 模型, 系统提示词: str):
        """构建基础对话智能体 - 使用LCEL链式组合"""
        try:
            if not LANGCHAIN_AVAILABLE:
                智能体服务日志器.warning("LangChain不可用，跳过基础对话智能体构建")
                return

            智能体服务日志器.info(f"🔧 开始构建基础对话智能体LCEL链: {self.实例ID}")

            # 1. 构建提示词模板 - 使用ChatPromptTemplate
            提示词模板 = self._构建LangChain提示词模板()
            if not 提示词模板:
                # 降级到简单模板
                ChatPromptTemplate = langchain_components.get_component(
                    "ChatPromptTemplate"
                )
                if ChatPromptTemplate:
                    提示词模板 = ChatPromptTemplate.from_messages(
                        [("system", 系统提示词), ("human", "{input}")]
                    )
                    智能体服务日志器.info("✅ 使用降级提示词模板")
                else:
                    智能体服务日志器.error("❌ ChatPromptTemplate不可用")

            # 2. 选择输出解析器
            if LANGCHAIN_AVAILABLE:
                输出解析器 = langchain_components.create_str_parser()
                if self.配置.输出格式 in ["json", "structured", "pydantic"]:
                    try:
                        if self.配置.自定义回复格式:
                            输出解析器 = langchain_components.create_json_parser()
                        else:
                            # 如果没有自定义格式，使用通用JSON解析器
                            输出解析器 = langchain_components.create_json_parser()
                    except Exception as e:
                        智能体服务日志器.warning(f"JSON解析器创建失败: {e}")
            else:
                输出解析器 = None

            # 3. 构建LCEL链 - prompt | model | parser
            self.基础对话链 = 提示词模板 | 模型 | 输出解析器
            self.当前活动链 = self.基础对话链

        except Exception as e:
            智能体服务日志器.error(f"❌ 构建基础对话智能体失败: {str(e)}")
            # 构建最简单的降级链
            try:
                ChatPromptTemplate = langchain_components.get_component(
                    "ChatPromptTemplate"
                )
                if ChatPromptTemplate and 模型:
                    简单提示词 = ChatPromptTemplate.from_messages(
                        [
                            ("system", "你是一个智能助手，请回答用户的问题。"),
                            ("human", "{input}"),
                        ]
                    )
                    输出解析器 = langchain_components.create_str_parser()
                    self.基础对话链 = 简单提示词 | 模型 | 输出解析器
                    self.当前活动链 = self.基础对话链
                    智能体服务日志器.info("✅ 使用降级基础对话链")
                else:
                    self.基础对话链 = None
                    self.当前活动链 = None
            except Exception as fallback_error:
                智能体服务日志器.error(f"❌ 降级链构建也失败: {str(fallback_error)}")
                self.基础对话链 = None
                self.当前活动链 = None

    async def _构建RAG智能体(self, 模型, 系统提示词: str):
        """构建RAG增强智能体 - 使用LCEL并行执行优化"""
        try:
            智能体服务日志器.info(
                f"🔧 [RAG链构建调试] 开始构建RAG增强智能体: {self.实例ID}"
            )

            if not LANGCHAIN_AVAILABLE:
                智能体服务日志器.warning(
                    "🔧 [RAG链构建调试] LangChain不可用，跳过RAG智能体构建"
                )
                return

            智能体服务日志器.info(
                "🔧 [RAG链构建调试] LangChain可用，开始构建RAG LCEL链"
            )

            # 1. 构建RAG检索函数
            async def rag_检索函数(输入: dict) -> str:
                """异步RAG检索函数"""
                try:
                    用户问题 = 输入.get("question", "") or 输入.get("input", "")
                    if not 用户问题:
                        return ""

                    # 执行RAG检索
                    检索结果 = await self._执行RAG检索(用户问题)
                    智能体服务日志器.debug(
                        f"🔍 RAG检索完成，上下文长度: {len(检索结果)}"
                    )
                    return 检索结果
                except Exception as e:
                    智能体服务日志器.warning(f"⚠️ RAG检索失败: {e}")
                    return ""

            # 2. 构建提示词模板 - 包含上下文和问题
            ChatPromptTemplate = langchain_components.get_component(
                "ChatPromptTemplate"
            )
            if ChatPromptTemplate:
                rag_提示词模板 = ChatPromptTemplate.from_messages(
                    [
                        ("system", 系统提示词 + "\n\n参考上下文：\n{context}"),
                        ("human", "{question}"),
                    ]
                )
            else:
                rag_提示词模板 = None

            # 3. 选择输出解析器
            if LANGCHAIN_AVAILABLE:
                输出解析器 = langchain_components.create_str_parser()
                if self.配置.输出格式 in ["json", "structured", "pydantic"]:
                    try:
                        if self.配置.自定义回复格式:
                            输出解析器 = langchain_components.create_json_parser()
                        else:
                            # 如果没有自定义格式，使用通用JSON解析器
                            输出解析器 = langchain_components.create_json_parser()
                    except Exception as e:
                        智能体服务日志器.warning(f"JSON解析器创建失败: {e}")
            else:
                输出解析器 = None

            # 4. 构建并行执行链 - 同时处理检索和问题传递
            if LANGCHAIN_AVAILABLE:
                并行处理 = langchain_components.create_runnable_parallel(
                    {
                        "context": langchain_components.create_runnable_lambda(
                            rag_检索函数
                        ),
                        "question": langchain_components.create_runnable_passthrough(),
                    }
                )
            else:
                并行处理 = None

            # 5. 构建完整的RAG链 - 并行检索 | 提示词 | 模型 | 解析器
            if LANGCHAIN_AVAILABLE and 并行处理 and 输出解析器:
                self.RAG链 = 并行处理 | rag_提示词模板 | 模型 | 输出解析器
                self.当前活动链 = self.RAG链
            else:
                self.RAG链 = None
                self.当前活动链 = None
                智能体服务日志器.warning("LangChain组件不完整，跳过RAG链构建")

        except Exception as e:
            智能体服务日志器.error(f"❌ 构建RAG智能体失败: {str(e)}")
            # 降级到基础对话链
            await self._构建基础对话智能体(模型, 系统提示词)
            智能体服务日志器.info("⚠️ RAG构建失败，已降级到基础对话模式")

    async def _构建工具智能体(self, 模型, 系统提示词: str):
        """构建工具调用智能体 - 集成内部函数工具"""
        try:
            if not LANGCHAIN_AVAILABLE:
                智能体服务日志器.warning("LangChain不可用，跳过工具智能体构建")
                return

            智能体服务日志器.info(f"🔧 开始构建工具调用智能体: {self.实例ID}")

            # 获取用户id用于工具调用
            用户id = getattr(self.配置, "用户表id", None) or getattr(
                self.配置, "用户id", 1
            )
            智能体服务日志器.info(f"工具调用用户id: {用户id}")

            # 统一的工具加载逻辑
            所有工具 = await self._加载智能体工具()

            if 所有工具:
                try:
                    # 使用 AgentExecutor 来正确处理工具调用
                    from langchain.agents import (
                        AgentExecutor,
                        create_tool_calling_agent,
                    )

                    # 构建提示词模板
                    ChatPromptTemplate = langchain_components.get_component(
                        "ChatPromptTemplate"
                    )
                    if ChatPromptTemplate:
                        提示词模板 = ChatPromptTemplate.from_messages(
                            [
                                ("system", 系统提示词),
                                ("human", "{input}"),
                                ("placeholder", "{agent_scratchpad}"),
                            ]
                        )
                    else:
                        智能体服务日志器.error("❌ ChatPromptTemplate不可用")
                        return

                    # 检查是否需要结构化输出
                    if hasattr(self, "json_schema") and self.json_schema:
                        # 创建支持结构化输出的工具调用智能体
                        智能体 = create_tool_calling_agent(模型, 所有工具, 提示词模板)

                        # 创建智能体执行器
                        基础执行器 = AgentExecutor(
                            agent=智能体,
                            tools=所有工具,
                            verbose=False,
                            handle_parsing_errors=True,
                        )

                        # 使用with_structured_output包装执行器，实现工具调用+JSON格式化
                        try:
                            import json

                            from pydantic import create_model

                            # 解析JSON Schema创建Pydantic模型
                            schema_dict = (
                                json.loads(self.json_schema)
                                if isinstance(self.json_schema, str)
                                else self.json_schema
                            )
                            properties = schema_dict.get("properties", {})
                            required = schema_dict.get("required", [])

                            # 动态创建Pydantic模型
                            fields = {}
                            for field_name, field_info in properties.items():
                                field_type = str  # 默认为字符串类型
                                if field_info.get("type") == "array":
                                    field_type = list
                                elif field_info.get("type") == "object":
                                    field_type = dict

                                default_value = field_info.get("default", ...)
                                if field_name not in required and default_value == ...:
                                    default_value = None
                                    field_type = field_type | None  # 可选字段

                                fields[field_name] = (field_type, default_value)

                            # 创建动态模型
                            ResponseModel = create_model("ResponseModel", **fields)

                            # 创建支持结构化输出的模型
                            结构化模型 = 模型.with_structured_output(ResponseModel)

                            # 重新创建智能体使用结构化模型
                            结构化智能体 = create_tool_calling_agent(
                                结构化模型, 所有工具, 提示词模板
                            )

                            # 创建最终的智能体执行器
                            self.智能体执行器 = AgentExecutor(
                                agent=结构化智能体,
                                tools=所有工具,
                                verbose=False,
                                handle_parsing_errors=True,
                            )

                            智能体服务日志器.info(
                                "✅ 创建支持工具调用+JSON格式化的智能体"
                            )

                        except Exception as e:
                            智能体服务日志器.warning(
                                f"结构化输出创建失败，使用基础执行器: {e}"
                            )
                            self.智能体执行器 = 基础执行器
                    else:
                        # 创建标准工具调用智能体
                        智能体 = create_tool_calling_agent(模型, 所有工具, 提示词模板)

                        # 创建智能体执行器
                        self.智能体执行器 = AgentExecutor(
                            agent=智能体,
                            tools=所有工具,
                            verbose=False,
                            handle_parsing_errors=True,
                        )

                    # 设置当前活动链为智能体执行器
                    self.当前活动链 = self.智能体执行器

                    智能体服务日志器.info(
                        f"✅ 工具调用智能体构建完成，绑定了 {len(所有工具)} 个工具"
                    )

                except Exception as e:
                    智能体服务日志器.warning(
                        f"AgentExecutor创建失败，降级为简单工具绑定: {e}"
                    )

                    # 降级方案：简单的工具绑定
                    带工具的模型 = 模型.bind_tools(所有工具)
                    ChatPromptTemplate = langchain_components.get_component(
                        "ChatPromptTemplate"
                    )
                    if ChatPromptTemplate:
                        提示词模板 = ChatPromptTemplate.from_messages(
                            [("system", 系统提示词), ("human", "{input}")]
                        )
                    else:
                        智能体服务日志器.error("❌ ChatPromptTemplate不可用")
                        return
                    self.工具链 = 提示词模板 | 带工具的模型
                    self.当前活动链 = self.工具链

            else:
                # 没有可用工具时，降级为基础对话模式
                智能体服务日志器.warning("没有可用工具，降级为基础对话模式")
                await self._构建基础对话智能体(模型, 系统提示词)

        except Exception as e:
            智能体服务日志器.error(f"❌ 构建工具智能体失败: {str(e)}")
            # 降级到基础对话链
            await self._构建基础对话智能体(模型, 系统提示词)
            智能体服务日志器.info("⚠️ 工具构建失败，已降级到基础对话模式")

    async def _加载智能体工具(self) -> List:
        """统一的智能体工具加载逻辑"""
        try:
            所有工具 = []

            # 1. 加载内部函数工具
            内部工具 = await self._加载内部函数工具()
            所有工具.extend(内部工具)

            # 2. 加载数据库配置工具
            配置工具 = await self._加载数据库配置工具()
            所有工具.extend(配置工具)

            智能体服务日志器.info(
                f"✅ 智能体工具加载完成: 内部工具 {len(内部工具)} 个, 配置工具 {len(配置工具)} 个"
            )
            return 所有工具

        except Exception as e:
            智能体服务日志器.error(f"加载智能体工具失败: {str(e)}")
            return []

    async def _加载内部函数工具(self) -> List:
        """加载用户可用的内部函数工具"""
        try:
            if not self.工具管理器:
                return []

            # 安全获取用户id：只能使用当前智能体配置中的用户id
            用户id = (
                getattr(self.配置, "用户表id", None)
                or getattr(self.配置, "用户id", None)
                or getattr(self.配置, "用户id", None)
            )

            if not (用户id and isinstance(用户id, int) and 用户id > 0):
                智能体服务日志器.warning("⚠️ 无效的用户id，跳过内部工具加载")
                return []

            # 初始化内部函数包装器
            if not 内部函数包装器实例.已初始化:
                await 内部函数包装器实例.初始化()

            # 已移除测试代码，使用内部函数包装器提供的工具
            可用内部工具 = []
            return 可用内部工具

        except Exception as e:
            智能体服务日志器.warning(f"获取内部函数工具失败: {e}")
            return []

    async def _加载数据库配置工具(self) -> List:
        """加载数据库中配置的工具 - 使用工具配置表ID"""
        try:
            if not LangChain工具数据层实例.已初始化:
                await LangChain工具数据层实例.初始化()

            # 获取智能体id
            智能体id = getattr(self.配置, "智能体id", None)
            if not 智能体id:
                智能体服务日志器.warning(
                    f"⚠️ 无法获取智能体id (当前值: {智能体id})，跳过数据库工具加载"
                )
                return []

            # 获取智能体的工具关联（包含工具配置表ID）
            工具关联列表 = await LangChain工具数据层实例.获取智能体工具关联(智能体id)

            配置工具 = []
            for 工具关联 in 工具关联列表:
                工具配置id = 工具关联.get("langchain_工具配置表id")
                工具名称 = 工具关联.get("工具名称")
                工具启用 = 工具关联.get("启用状态", True)

                if 工具配置id and 工具名称 and 工具启用:
                    try:
                        # 获取工具配置详情
                        工具配置 = await LangChain工具数据层实例.根据ID获取工具配置(
                            工具配置id
                        )
                        if not 工具配置:
                            智能体服务日志器.warning(
                                f"⚠️ 工具配置不存在: ID={工具配置id}"
                            )
                            continue

                        # 使用统一的工具加载方法
                        工具实例 = await self._加载工具实例(工具名称, 工具配置id)

                        if 工具实例:
                            配置工具.append(工具实例)
                            智能体服务日志器.debug(
                                f"✅ 工具加载成功: {工具名称} (ID: {工具配置id})"
                            )

                    except Exception as 工具加载错误:
                        智能体服务日志器.error(
                            f"加载工具失败 {工具名称} (ID: {工具配置id}): {str(工具加载错误)}"
                        )
                        continue

            智能体服务日志器.info(f"✅ 从数据库加载了 {len(配置工具)} 个工具")
            return 配置工具

        except Exception as e:
            智能体服务日志器.warning(f"从数据库加载工具失败: {e}")
            return []

    async def 对话(self, 用户输入: str, 会话id: Optional[str] = None) -> Dict[str, Any]:
        """处理用户对话 - 使用LCEL链，确保所有变量都有默认值"""
        try:
            self.最后活动时间 = datetime.now()
            self.对话次数 += 1

            # 确保LCEL链可用
            if not self.当前活动链 or not LANGCHAIN_AVAILABLE:
                raise Exception("LCEL链不可用，需要重新初始化智能体")

            智能体服务日志器.info(f"🔗 [对话调试] 使用LCEL链处理对话: {self.实例ID}")
            智能体服务日志器.info(f"🔗 [对话调试] 当前活动链: {type(self.当前活动链)}")
            智能体服务日志器.info(
                f"🔗 [对话调试] 是否有RAG链: {hasattr(self, 'RAG链')}"
            )
            if hasattr(self, "RAG链"):
                智能体服务日志器.info(f"🔗 [对话调试] RAG链对象: {self.RAG链}")
                智能体服务日志器.info(
                    f"🔗 [对话调试] 当前链是否为RAG链: {self.当前活动链 == self.RAG链}"
                )

            # 准备输入数据 - 根据当前链类型自动选择格式
            if hasattr(self, "RAG链") and self.当前活动链 == self.RAG链:
                # RAG模式：使用question键，链内部会处理并行检索
                智能体服务日志器.info(
                    "🔗 [对话调试] 使用RAG模式，输入格式: {'question': 用户输入}"
                )
                链输入 = {"question": 用户输入}
            else:
                # 基础对话和工具模式：使用input键，确保所有变量都有默认值
                智能体服务日志器.info(
                    "🔗 [对话调试] 使用基础对话/工具模式，输入格式: {'input': 用户输入}"
                )
                链输入 = self._构建完整链输入(用户输入)

            # 调用LCEL链
            开始时间 = datetime.now()
            链响应 = await self.当前活动链.ainvoke(链输入)
            处理时长 = (datetime.now() - 开始时间).total_seconds()

            # 处理响应
            处理后响应 = ""
            工具调用信息 = []

            # 检查是否是AgentExecutor的响应
            if isinstance(链响应, dict) and "output" in 链响应:
                # AgentExecutor响应格式
                处理后响应 = 链响应.get("output", "")

                # 检查是否有中间步骤（工具调用）
                if "intermediate_steps" in 链响应:
                    中间步骤 = 链响应["intermediate_steps"]
                    for 步骤 in 中间步骤:
                        if len(步骤) >= 2:
                            动作, 结果 = 步骤[0], 步骤[1]
                            if hasattr(动作, "tool") and hasattr(动作, "tool_input"):
                                工具调用信息.append(
                                    {
                                        "工具名称": 动作.tool,
                                        "调用参数": 动作.tool_input,
                                        "调用状态": "已完成",
                                        "调用结果": str(结果)[:200],  # 限制结果长度
                                    }
                                )

                智能体服务日志器.info(
                    f"🔧 AgentExecutor执行完成，工具调用数: {len(工具调用信息)}"
                )

            elif hasattr(链响应, "tool_calls"):
                # 简单工具绑定的响应
                tool_calls = getattr(链响应, "tool_calls", [])
                if tool_calls:
                    智能体服务日志器.info(f"🔧 检测到 {len(tool_calls)} 个工具调用")
                    for tool_call in tool_calls:
                        工具名称 = (
                            tool_call.get("name", "")
                            if isinstance(tool_call, dict)
                            else str(tool_call)
                        )
                        工具参数 = (
                            tool_call.get("args", {})
                            if isinstance(tool_call, dict)
                            else {}
                        )
                        工具调用信息.append(
                            {
                                "工具名称": 工具名称,
                                "调用参数": 工具参数,
                                "调用状态": "已调用",
                            }
                        )
                处理后响应 = getattr(链响应, "content", "工具调用已执行")

            else:
                # 普通对话响应
                if hasattr(链响应, "content"):
                    处理后响应 = getattr(链响应, "content", str(链响应))
                elif isinstance(链响应, dict):
                    处理后响应 = str(链响应)
                else:
                    处理后响应 = str(链响应)

            # 保存对话历史
            self._保存对话历史(用户输入, 处理后响应, 会话id)

            return {
                "success": True,
                "response": 处理后响应,
                "session_id": 会话id,
                "processing_time": 处理时长,
                "token_count": len(处理后响应) // 4,  # 估算token数
                "knowledge_used": self.配置.启用rag,
                "output_format": self.配置.输出格式,
                "structured_output": isinstance(链响应, dict),
                "output_mode": "lcel_chain",
                "model_name": self.配置.模型名称,
                "model_provider": "lcel",
                "api_status": "success",
                "chain_type": "basic_agent",
                "tool_calls": 工具调用信息,
                "has_tool_calls": len(工具调用信息) > 0,
            }

        except Exception as e:
            self.错误次数 += 1
            智能体服务日志器.error(f"❌ LCEL链对话失败: {str(e)}")
            # 重新抛出异常，让上层处理
            raise e

    def _构建完整链输入(self, 用户输入: str) -> Dict[str, Any]:
        """构建完整的链输入，确保所有变量都有默认值"""
        try:
            # 构建标准变量字典，包含所有可能需要的变量
            变量字典 = self._构建标准变量字典("", 用户输入, self.自定义变量)

            # 基础输入
            链输入 = {"input": 用户输入}

            # 添加所有变量作为链输入的一部分
            链输入.update(变量字典)

            智能体服务日志器.debug(f"🔧 构建完整链输入: {len(链输入)} 个变量")

            return 链输入

        except Exception as e:
            智能体服务日志器.error(f"❌ 构建链输入失败: {str(e)}")
            # 返回最基础的输入
            return {"input": 用户输入}

    async def 流式对话(self, 用户输入: str, 会话id: Optional[str] = None):
        """流式对话 - 使用LCEL的astream方法实现增量输出"""
        try:
            self.最后活动时间 = datetime.now()
            self.对话次数 += 1

            # 检查是否支持流式输出
            if not self.当前活动链 or not LANGCHAIN_AVAILABLE:
                # 降级到非流式模式
                智能体服务日志器.warning("⚠️ LCEL链不可用，降级到非流式对话")
                结果 = await self.对话(用户输入, 会话id)
                yield {
                    "type": "complete",
                    "content": 结果.get("response", ""),
                    "metadata": 结果,
                }
                return

            智能体服务日志器.info(f"🌊 开始流式对话: {self.实例ID}")

            # 准备输入数据 - 根据当前链类型自动选择格式
            if hasattr(self, "RAG链") and self.当前活动链 == self.RAG链:
                链输入 = 用户输入
            else:
                链输入 = {"input": 用户输入}

            # 流式处理
            完整响应 = ""
            开始时间 = datetime.now()

            try:
                # 使用LCEL的astream方法进行流式输出
                async for 块 in self.当前活动链.astream(链输入):
                    if isinstance(块, dict):
                        # 处理字典类型的块
                        块内容 = str(块)
                    else:
                        # 处理字符串类型的块
                        块内容 = str(块)

                    完整响应 += 块内容

                    # 发送流式块
                    yield {
                        "type": "chunk",
                        "content": 块内容,
                        "metadata": {
                            "chunk_size": len(块内容),
                            "total_length": len(完整响应),
                            "chain_type": "basic_agent",
                        },
                    }

                处理时长 = (datetime.now() - 开始时间).total_seconds()

                # 保存对话历史
                self._保存对话历史(用户输入, 完整响应, 会话id)

                # 发送完成信号
                yield {
                    "type": "complete",
                    "content": "",
                    "metadata": {
                        "success": True,
                        "total_response": 完整响应,
                        "processing_time": 处理时长,
                        "token_count": len(完整响应) // 4,
                        "knowledge_used": self.配置.启用rag,
                        "output_format": self.配置.输出格式,
                        "output_mode": "lcel_stream",
                        "model_name": self.配置.模型名称,
                        "chain_type": "basic_agent",
                    },
                }

                智能体服务日志器.info(
                    f"✅ 流式对话完成，总耗时: {处理时长:.2f}秒，响应长度: {len(完整响应)}"
                )

            except Exception as e:
                智能体服务日志器.error(f"❌ 流式处理失败: {e}")
                yield {
                    "type": "error",
                    "content": f"流式处理失败: {str(e)}",
                    "metadata": {
                        "success": False,
                        "error": str(e),
                        "partial_response": 完整响应,
                    },
                }

        except Exception as e:
            self.错误次数 += 1
            错误日志器.error(f"流式对话失败 {self.实例ID}: {str(e)}")
            yield {
                "type": "error",
                "content": f"抱歉，流式对话处理时发生错误：{str(e)}",
                "metadata": {"success": False, "error": str(e)},
            }

    async def _调用模型(
        self, _模型, 用户输入: str, 知识库上下文: str = "", 会话id: Optional[str] = None
    ) -> Dict[str, Any]:
        """调用模型生成回复 - 支持结构化输出，使用Context7优化的消息构建"""
        # 标记未使用参数，保持接口一致性
        _ = _模型  # 保留参数用于接口兼容性
        try:
            # Context7最佳实践：直接构建标准化消息列表，避免字符串解析
            消息列表 = []

            # 1. 构建系统消息 - 使用完整的系统提示词
            系统提示词 = self._构建系统提示词(知识库上下文, 用户输入)
            if 系统提示词:
                消息列表.append({"role": "system", "content": 系统提示词})
                智能体服务日志器.info(f"✅ 系统提示词已添加: {len(系统提示词)} 字符")
                智能体服务日志器.debug(f"🔍 系统提示词内容: {系统提示词[:200]}...")

            # 2. 添加历史对话（如果有会话ID）
            if 会话id and self.对话历史:
                最近对话 = [
                    h
                    for h in self.对话历史[-self.配置.记忆窗口大小 :]
                    if h.get("会话id") == 会话id
                ]
                for 历史 in 最近对话:
                    消息列表.append({"role": "user", "content": 历史["用户输入"]})
                    消息列表.append(
                        {"role": "assistant", "content": 历史["智能体回复"]}
                    )
                智能体服务日志器.info(f"📚 历史对话已添加: {len(最近对话)} 轮对话")

            # 3. 添加当前用户输入
            if self.配置.用户提示词:
                # 如果有自定义用户提示词模板，进行变量替换（包括{input}）
                用户消息内容 = self._替换用户提示词变量(
                    self.配置.用户提示词, 知识库上下文, 用户输入
                )
                # 最后替换{input}为实际用户输入
                用户消息内容 = 用户消息内容.replace("{input}", 用户输入)
                消息列表.append({"role": "user", "content": 用户消息内容})
                智能体服务日志器.info("✅ 自定义用户提示词已应用，用户输入已替换")
                智能体服务日志器.debug(f"🔍 用户消息内容: {用户消息内容[:200]}...")
            else:
                # 使用原始用户输入
                消息列表.append({"role": "user", "content": 用户输入})

            智能体服务日志器.info(f"📚 消息列表构建完成: {len(消息列表)} 条消息")

            # 详细记录每条消息（调试模式）
            if 智能体服务日志器.isEnabledFor(logging.DEBUG):
                for i, 消息 in enumerate(消息列表):
                    角色 = 消息["role"]
                    内容预览 = (
                        消息["content"][:100] + "..."
                        if len(消息["content"]) > 100
                        else 消息["content"]
                    )
                    智能体服务日志器.debug(f"📨 消息 {i + 1} ({角色}): {内容预览}")

            # 准备结构化输出配置
            结构化输出模式 = None
            pydantic_模型类 = None

            # 检查是否需要结构化输出 - 新的数据结构
            # 现在自定义回复格式直接就是JSON Schema，不再是包装对象
            if self.配置.自定义回复格式 and isinstance(self.配置.自定义回复格式, dict):
                # 检查是否有properties字段，表示这是一个JSON Schema
                if "properties" in self.配置.自定义回复格式:
                    json_schema = self.配置.自定义回复格式
                    智能体服务日志器.info("🔧 启用结构化输出 (新数据结构)")
                    try:
                        智能体服务日志器.info(
                            f"🔍 JSON Schema: {json.dumps(json_schema, ensure_ascii=False, indent=2)}"
                        )
                        pydantic_模型类 = self._从JSON_Schema创建Pydantic模型(
                            json_schema
                        )
                        if pydantic_模型类:
                            结构化输出模式 = "with_structured_output"
                            智能体服务日志器.info(
                                f"✅ Pydantic模型创建成功: {pydantic_模型类.__name__}"
                            )

                            # 详细打印模型字段信息
                            智能体服务日志器.info(
                                f"📋 模型字段列表: {list(pydantic_模型类.model_fields.keys())}"
                            )
                            for (
                                字段名,
                                字段信息,
                            ) in pydantic_模型类.model_fields.items():
                                智能体服务日志器.info(
                                    f"🔧 字段: {字段名} -> {字段信息.annotation}"
                                )
                        else:
                            智能体服务日志器.error("❌ Pydantic模型创建失败，返回None")
                    except Exception as e:
                        智能体服务日志器.error(f"❌ 结构化输出配置失败: {str(e)}")
                        智能体服务日志器.debug(
                            f"🔍 错误详情: {type(e).__name__}: {str(e)}"
                        )
                else:
                    智能体服务日志器.info(
                        "📝 自定义回复格式不是JSON Schema，使用普通文本模式"
                    )
            else:
                智能体服务日志器.info("📝 未配置自定义回复格式，使用普通文本模式")

            # 调用模型管理器
            智能体服务日志器.info(f"🚀 准备调用模型管理器: {self.配置.模型名称}")
            智能体服务日志器.info(
                f"🔧 调用参数: 结构化输出模式={结构化输出模式}, 温度={self.配置.温度参数}"
            )

            try:
                响应结果 = await LangChain模型管理器实例.调用模型(
                    self.配置.模型名称,
                    消息列表,
                    结构化输出模式=结构化输出模式,
                    pydantic_模型类=pydantic_模型类,
                    temperature=self.配置.温度参数,
                    max_tokens=self.配置.最大令牌数,
                )

                # 仅在调试模式下输出详细信息
                if 响应结果.get("success"):
                    智能体服务日志器.debug(
                        f"模型调用成功: {响应结果.get('model_name')}"
                    )
                else:
                    智能体服务日志器.warning(
                        f"模型调用失败: {响应结果.get('error', '未知错误')}"
                    )
                # 移除冗余的详细日志输出

            except Exception as model_error:
                错误日志器.error(
                    f"模型管理器调用异常: {str(model_error)}", exc_info=True
                )
                return {
                    "success": False,
                    "response": f"抱歉，模型管理器调用异常：{str(model_error)}",
                    "structured_output": False,
                    "output_mode": "error",
                    "model_name": self.配置.模型名称,
                    "api_status": "exception",
                    "error": str(model_error),
                }

            if 响应结果.get("success"):
                return 响应结果
            else:
                错误信息 = 响应结果.get("error", "未知错误")
                错误日志器.error(f"模型调用失败: {错误信息}")
                return {
                    "success": False,
                    "response": f"抱歉，模型 {self.配置.模型名称} 调用时出现错误：{错误信息}",
                    "structured_output": False,
                    "output_mode": "error",
                    "model_name": self.配置.模型名称,
                    "api_status": "failed",
                    "error": 错误信息,
                }

        except Exception as e:
            智能体服务日志器.error(f"❌ 调用模型失败: {str(e)}")
            return {
                "success": False,
                "response": "抱歉，模型调用时出现错误，请稍后重试。",
                "structured_output": False,
                "output_mode": "error",
                "model_name": self.配置.模型名称,
                "error": str(e),
                "api_status": "exception",
            }

    def _从JSON_Schema创建Pydantic模型(self, json_schema: Dict[str, Any]):
        """
        从JSON Schema创建Pydantic v2模型类 - Context7优化版本
        使用最新的Pydantic v2语法和LangChain最佳实践
        """
        try:
            # 使用现代Pydantic v2 - 与LangChain最新版本完全兼容
            from pydantic import ConfigDict, Field, create_model

            智能体服务日志器.info("🔧 开始创建Pydantic v2模型 (Context7优化版本)")

            # 参数验证 - Context7最佳实践
            if not self._验证JSON_Schema格式(json_schema):
                智能体服务日志器.error("❌ JSON Schema格式验证失败")
                return None

            properties = json_schema.get("properties", {})
            required_fields = json_schema.get("required", [])

            智能体服务日志器.info(
                f"📋 解析字段: {len(properties)}个属性, {len(required_fields)}个必需字段"
            )

            # 构建字段定义 - 使用Pydantic v2语法
            field_definitions = {}

            for field_name, field_schema in properties.items():
                field_type = field_schema.get("type", "string")
                field_description = field_schema.get("description", "")
                field_default = field_schema.get("default")
                is_required = field_name in required_fields

                # 映射JSON Schema类型到Python类型
                if field_type == "string":
                    python_type = str
                elif field_type == "integer":
                    python_type = int
                elif field_type == "number":
                    python_type = float
                elif field_type == "boolean":
                    python_type = bool
                elif field_type == "array":
                    items_schema = field_schema.get("items", {})
                    items_type = items_schema.get("type", "string")

                    if items_type == "string":
                        python_type = List[str]
                    elif items_type == "integer":
                        python_type = List[int]
                    elif items_type == "number":
                        python_type = List[float]
                    elif items_type == "object":
                        # 处理数组对象类型，创建嵌套模型
                        if "properties" in items_schema:
                            # 递归创建嵌套对象的Pydantic模型
                            nested_field_definitions = {}
                            nested_properties = items_schema["properties"]
                            nested_required = items_schema.get("required", [])

                            for (
                                nested_field_name,
                                nested_field_schema,
                            ) in nested_properties.items():
                                nested_field_type = nested_field_schema.get(
                                    "type", "string"
                                )
                                nested_field_description = nested_field_schema.get(
                                    "description", ""
                                )
                                nested_is_required = (
                                    nested_field_name in nested_required
                                )

                                # 映射嵌套字段类型
                                if nested_field_type == "string":
                                    nested_python_type = str
                                elif nested_field_type == "integer":
                                    nested_python_type = int
                                elif nested_field_type == "number":
                                    nested_python_type = float
                                elif nested_field_type == "boolean":
                                    nested_python_type = bool
                                else:
                                    nested_python_type = str

                                if not nested_is_required:
                                    nested_python_type = Optional[nested_python_type]

                                nested_field_definitions[nested_field_name] = (
                                    nested_python_type,
                                    Field(description=nested_field_description),
                                )

                            # 创建嵌套模型
                            nested_model_name = f"{field_name}Item"
                            nested_model = create_model(
                                nested_model_name, **nested_field_definitions
                            )
                            python_type = List[nested_model]
                        else:
                            python_type = List[Dict[str, Any]]
                    else:
                        python_type = List[Any]
                elif field_type == "object":
                    python_type = Dict[str, Any]
                else:
                    python_type = str

                # 如果不是必需字段，使用Optional
                if not is_required:
                    python_type = Optional[python_type]

                # 构建Field参数 - Pydantic v2语法
                field_kwargs = {"description": field_description}

                # 添加约束条件
                minimum = field_schema.get("minimum")
                maximum = field_schema.get("maximum")
                min_length = field_schema.get("minLength")
                max_length = field_schema.get("maxLength")

                if field_type in ["integer", "number"] and minimum is not None:
                    field_kwargs["ge"] = minimum
                if field_type in ["integer", "number"] and maximum is not None:
                    field_kwargs["le"] = maximum
                if field_type == "string" and min_length is not None:
                    field_kwargs["min_length"] = min_length
                if field_type == "string" and max_length is not None:
                    field_kwargs["max_length"] = max_length

                # 创建Field定义
                if is_required:
                    if field_default is not None:
                        field_def = (
                            python_type,
                            Field(default=field_default, **field_kwargs),
                        )
                    else:
                        field_def = (python_type, Field(**field_kwargs))
                else:
                    default_value = field_default if field_default is not None else None
                    field_def = (
                        python_type,
                        Field(default=default_value, **field_kwargs),
                    )

                field_definitions[field_name] = field_def

            # 动态创建Pydantic v2模型 - Context7最新语法
            model_name = json_schema.get("title", "DynamicStructuredOutput")
            model_description = json_schema.get(
                "description", "动态生成的结构化输出模型"
            )

            # 创建模型配置 - 使用Pydantic v2最新语法
            model_config = ConfigDict(
                str_strip_whitespace=True,  # 自动去除字符串首尾空格
                validate_assignment=True,  # 验证赋值
                extra="forbid",  # 禁止额外字段
                use_enum_values=True,  # 使用枚举值
                arbitrary_types_allowed=False,  # 禁止任意类型以确保序列化安全
                json_schema_extra={  # 额外的JSON Schema信息
                    "examples": []  # 示例数据由结构化输出处理器统一生成
                },
            )

            # 动态创建模型 - 使用正确的Pydantic v2语法
            dynamic_model = create_model(
                model_name,
                **field_definitions,
                __config__=model_config,  # Pydantic v2仍支持__config__，但推荐使用model_config
            )

            # 设置模型配置和文档字符串 - Context7最佳实践
            dynamic_model.model_config = model_config
            dynamic_model.__doc__ = model_description

            # 验证模型创建成功
            if not self._验证Pydantic模型(dynamic_model, field_definitions):
                智能体服务日志器.error("❌ Pydantic模型验证失败")
                return None

            智能体服务日志器.info(f"✅ 成功创建Pydantic v2模型: {model_name}")
            智能体服务日志器.info(f"📋 字段定义: {list(field_definitions.keys())}")
            智能体服务日志器.debug("🔧 模型配置: 兼容LangChain结构化输出")

            return dynamic_model

        except Exception as e:
            智能体服务日志器.error(f"❌ 创建Pydantic模型失败: {str(e)}")
            智能体服务日志器.debug(f"🔍 错误详情: {type(e).__name__}: {str(e)}")
            return None

    def _验证JSON_Schema格式(self, json_schema: Dict[str, Any]) -> bool:
        """
        验证JSON Schema格式 - Context7最佳实践
        """
        try:
            # 基本格式检查
            if not isinstance(json_schema, dict):
                智能体服务日志器.error("❌ JSON Schema必须是字典格式")
                return False

            # 必需字段检查
            if "properties" not in json_schema:
                智能体服务日志器.error("❌ JSON Schema缺少properties字段")
                return False

            properties = json_schema.get("properties", {})
            if not isinstance(properties, dict):
                智能体服务日志器.error("❌ properties字段必须是字典格式")
                return False

            if len(properties) == 0:
                智能体服务日志器.warning("⚠️ JSON Schema没有定义任何属性")
                return False

            # 验证每个属性的格式
            for field_name, field_def in properties.items():
                if not isinstance(field_def, dict):
                    智能体服务日志器.error(f"❌ 字段 {field_name} 定义必须是字典格式")
                    return False

                if "type" not in field_def:
                    智能体服务日志器.error(f"❌ 字段 {field_name} 缺少type定义")
                    return False

            智能体服务日志器.info("✅ JSON Schema格式验证通过")
            return True

        except Exception as e:
            智能体服务日志器.error(f"❌ JSON Schema格式验证异常: {str(e)}")
            return False

    def _验证Pydantic模型(
        self, _model_class, _field_definitions: Dict[str, Any]
    ) -> bool:
        """
        验证Pydantic模型创建成功 - Context7最佳实践
        """
        # 标记未使用参数，保持接口一致性
        _ = _field_definitions  # 保留参数用于接口兼容性
        try:
            # 检查模型类是否有效
            if not hasattr(_model_class, "model_validate"):
                智能体服务日志器.error("❌ 模型类缺少model_validate方法")
                return False

            # 使用结构化输出处理器生成示例数据 - 消除冗余代码
            try:
                示例数据 = LangChain结构化输出处理器实例._生成模型示例数据(_model_class)
                示例实例 = _model_class.model_validate(示例数据)
                智能体服务日志器.info("✅ Pydantic模型验证成功")
                智能体服务日志器.debug(f"🔍 示例实例: {示例实例.model_dump()}")
                return True
            except Exception as e:
                智能体服务日志器.error(f"❌ 模型实例化失败: {str(e)}")
                智能体服务日志器.debug(f"🔍 示例数据: {示例数据}")
                return False

        except Exception as e:
            智能体服务日志器.error(f"❌ Pydantic模型验证异常: {str(e)}")
            return False

    def _支持JSON模式(self) -> bool:
        """检查模型是否支持原生JSON模式"""
        # 支持JSON模式的模型列表
        支持JSON的模型 = [
            "gpt-4",
            "gpt-4-turbo",
            "gpt-4o",
            "gpt-3.5-turbo",
            "qwen-turbo",
            "qwen-plus",
            "qwen-max",
        ]

        return any(模型名 in self.配置.模型名称 for 模型名 in 支持JSON的模型)

    def _获取向量存储类型(self) -> str:
        """获取当前向量存储类型"""
        return "PostgreSQL"

    async def _获取智能体关联知识库列表(self, 智能体id: int) -> List[str]:
        """通过关联表获取智能体关联的知识id列表 - 优化版本"""
        try:
            from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

            查询SQL = """
            SELECT langchain_知识库表id
            FROM langchain_智能体知识库关联表
            WHERE langchain_智能体配置表id = $1 AND 状态 = 'active'
            ORDER BY 权重 DESC, id ASC
            """

            结果 = await 异步连接池实例.执行查询(查询SQL, (智能体id,))
            知识id列表 = [str(行["langchain_知识库表id"]) for 行 in 结果 or []]

            智能体服务日志器.debug(
                f"智能体 {智能体id} 关联知识库: {len(知识id列表)} 个"
            )
            return 知识id列表

        except Exception as e:
            智能体服务日志器.error(f"获取智能体关联知识库列表失败: {str(e)}")
            return []

    def _保存对话历史(self, 用户输入: str, 智能体回复: str, 会话id: Optional[str]):
        """保存对话历史"""
        对话记录 = {
            "时间": datetime.now(),
            "会话id": 会话id,
            "用户输入": 用户输入,
            "智能体回复": 智能体回复,
        }
        self.对话历史.append(对话记录)

        # 限制历史记录数量
        if len(self.对话历史) > self.配置.记忆窗口大小 * 2:
            self.对话历史 = self.对话历史[-self.配置.记忆窗口大小 :]

    async def 销毁(self):
        """销毁智能体实例"""
        try:
            self.状态 = 智能体状态.已销毁

            # 清理资源
            if self.智能体执行器:
                self.智能体执行器 = None
            if self.对话链:
                self.对话链 = None

            self.对话历史.clear()

            智能体服务日志器.info(f"智能体实例已销毁: {self.实例ID}")

        except Exception as e:
            错误日志器.error(f"销毁智能体实例失败 {self.实例ID}: {str(e)}")

    def 获取统计信息(self) -> Dict[str, Any]:
        """获取智能体统计信息"""
        return {
            "实例ID": self.实例ID,
            "状态": self.状态.value,
            "创建时间": self.创建时间.isoformat(),
            "最后活动时间": self.最后活动时间.isoformat(),
            "对话次数": self.对话次数,
            "总令牌消耗": self.总令牌消耗,
            "错误次数": self.错误次数,
            "配置信息": {
                "智能体名称": self.配置.智能体名称,
                "模型名称": self.配置.模型名称,
                "启用rag": self.配置.启用rag,
            },
        }

    async def _执行RAG检索(self, 用户输入: str) -> str:
        """执行RAG检索获取知识库上下文 - 优化检索策略和错误处理"""
        try:
            # 检查RAG配置
            if not self.配置.启用rag:
                return ""

            # 解析智能体id并获取配置
            智能体id = int(self.实例ID) if self.实例ID.isdigit() else None
            if not 智能体id:
                return ""

            # 导入必要的模块
            from 服务.LangChain_RAG测试工具 import RAG测试工具

            # 获取知识库列表和关联配置
            知识库列表 = []
            关联配置 = {}

            try:
                知识库列表 = await self._获取智能体关联知识库列表(智能体id)
                关联配置 = await RAG测试工具.获取智能体关联配置(智能体id)
            except Exception as e:
                智能体服务日志器.warning(f"获取RAG配置失败: {str(e)}")
                return ""

            if not 知识库列表:
                return ""

            智能体服务日志器.info(
                f"🔍 RAG检索开始: 查询='{用户输入[:50]}{'...' if len(用户输入) > 50 else ''}', 知识库数量={len(知识库列表)}"
            )

            # 使用统一的检索参数构建方法
            检索参数 = self._构建检索参数(关联配置)

            # 执行知识库检索
            检索统计 = {"成功": 0, "失败": 0, "总片段": 0}
            检索上下文 = []

            try:
                # 遍历所有关联的知识库
                for 知识id in 知识库列表:
                    try:
                        知识id_int = int(知识id)

                        检索结果 = await LangChain知识库服务实例.智能体检索测试(
                            智能体id,
                            用户输入,
                            检索参数,
                        )

                        if 检索结果.get("success") and 检索结果.get("data"):
                            文档片段 = 检索结果["data"].get("检索结果", [])
                            有效片段 = []

                            for 片段 in 文档片段:
                                # 修复字段映射：使用正确的字段名
                                相似度 = 片段.get("相似度分数", 0.0)
                                内容 = 片段.get(
                                    "分块内容", ""
                                ).strip()  # 修复：使用分块内容而不是内容

                                # 质量过滤
                                if (
                                    相似度 >= 检索参数["相似度阈值"]
                                    and 内容
                                    and len(内容) > 10
                                ):
                                    # 修复字段映射：直接从片段获取文档信息
                                    文档名称 = 片段.get("文档名称", "未知文档")
                                    文档UUID = 片段.get("文档uuid", "")
                                    分块序号 = 片段.get("分块序号", 0)

                                    # 解析元数据（如果存在）
                                    元数据 = 片段.get("元数据", {})
                                    if isinstance(元数据, str):
                                        try:
                                            import json

                                            元数据 = json.loads(元数据)
                                        except (json.JSONDecodeError, ValueError):
                                            元数据 = {}

                                    有效片段.append(
                                        {
                                            "知识id": 知识id_int,
                                            "文档标题": 文档名称,
                                            "文档UUID": 文档UUID,
                                            "内容": 内容,
                                            "相似度": round(相似度, 3),
                                            "来源": f"知识库{知识id_int}({文档名称})",
                                            "存储架构": "PostgreSQL向量存储",
                                            "分块序号": 分块序号,
                                            "元数据": 元数据,
                                        }
                                    )

                            检索上下文.extend(有效片段)
                            检索统计["成功"] += 1
                            检索统计["总片段"] += len(有效片段)

                    except Exception as e:
                        检索统计["失败"] += 1
                        智能体服务日志器.warning(f"知识库 {知识id} 检索失败: {str(e)}")
                        continue

                # 处理检索结果
                if 检索上下文:
                    优化后上下文 = await self._优化检索结果(
                        检索上下文, 检索参数["最大检索数量"]
                    )

                    # 检查结果质量并决定是否需要回退
                    需要回退 = await self._检查是否需要智能回退(
                        优化后上下文, 用户输入, 检索参数
                    )

                    if 需要回退:
                        智能体服务日志器.info("🔄 检索结果不足，启动智能回退机制")
                        回退上下文 = await self._执行智能回退(用户输入, 优化后上下文)
                        上下文文本 = self._构建知识库上下文文本(
                            优化后上下文, 回退上下文
                        )
                    else:
                        上下文文本 = self._构建知识库上下文文本(优化后上下文)

                    智能体服务日志器.info(
                        f"✅ RAG检索完成: {len(优化后上下文)} 个片段，上下文长度 {len(上下文文本)} 字符，"
                        f"智能回退: {'启用' if 需要回退 else '未启用'}"
                    )
                    return 上下文文本
                else:
                    # 没有检索到任何结果，直接启动智能回退
                    智能体服务日志器.warning("⚠️ 未检索到任何结果，启动智能回退机制")
                    回退上下文 = await self._执行智能回退(用户输入, [])
                    if 回退上下文:
                        上下文文本 = self._构建知识库上下文文本([], 回退上下文)
                        智能体服务日志器.info(
                            f"✅ 智能回退完成: 上下文长度 {len(上下文文本)} 字符"
                        )
                        return 上下文文本

            except ImportError as e:
                智能体服务日志器.warning(
                    f"⚠️ 知识库服务不可用，尝试降级到RAG引擎: {str(e)}"
                )

                # 降级到RAG引擎
                return await self._RAG引擎降级检索(用户输入, 检索参数)

            return ""

        except Exception as e:
            错误响应 = 智能体错误处理器.处理RAG检索错误(e, 智能体id or 0)
            智能体服务日志器.error(f"RAG检索失败: {错误响应['error_message']}")
            return ""

    async def _优化检索结果(self, 检索结果: List[Dict], 最大数量: int) -> List[Dict]:
        """优化检索结果 - 去重、排序、质量筛选"""
        try:
            if not 检索结果:
                return []

            # 1. 去重处理
            去重结果 = []
            seen_contents = set()

            for 片段 in 检索结果:
                内容 = 片段.get("内容", "").strip()
                if not 内容:
                    continue

                # 生成内容指纹（前100字符）
                内容指纹 = 内容[:100]

                if 内容指纹 not in seen_contents:
                    seen_contents.add(内容指纹)
                    去重结果.append(片段)
                else:
                    # 如果内容重复，保留相似度更高的
                    for i, 已有片段 in enumerate(去重结果):
                        if 已有片段.get("内容", "")[:100] == 内容指纹:
                            if 片段.get("相似度", 0) > 已有片段.get("相似度", 0):
                                去重结果[i] = 片段
                            break

            # 2. 按相似度和内容质量排序
            def 质量评分(片段):
                相似度 = 片段.get("相似度", 0)
                内容长度 = len(片段.get("内容", ""))
                # 综合评分：相似度权重80%，内容长度权重20%
                长度评分 = min(内容长度, 1000) / 1000  # 最大1000字符得满分
                return 相似度 * 0.8 + 长度评分 * 0.2

            去重结果.sort(key=质量评分, reverse=True)

            # 3. 选择最佳结果
            最终结果 = 去重结果[:最大数量]

            智能体服务日志器.debug(
                f"🎯 结果优化: {len(检索结果)} → {len(去重结果)} → {len(最终结果)}"
            )

            return 最终结果

        except Exception as e:
            智能体服务日志器.error(f"❌ 优化检索结果异常: {str(e)}")
            # 降级处理：简单按相似度排序
            return sorted(检索结果, key=lambda x: x.get("相似度", 0), reverse=True)[
                :最大数量
            ]

    async def _RAG引擎降级检索(self, 用户输入: str, 检索参数: Dict) -> str:
        """RAG引擎降级检索"""
        try:
            # 初始化RAG引擎（如果需要）
            if not RAG引擎实例.已初始化:
                智能体id = int(self.实例ID) if self.实例ID.isdigit() else 1
                await RAG引擎实例.初始化(智能体id=智能体id)

            # 执行检索（支持查询优化）
            查询优化配置 = 检索参数.get("查询优化配置", {})
            检索结果 = await RAG引擎实例.检索(
                用户输入,
                None,  # 知识id由RAG引擎自动确定
                检索参数["最大检索数量"],
                查询优化配置,
            )

            # 构建知识库上下文
            if 检索结果:
                上下文片段 = []
                for i, 文档 in enumerate(检索结果, 1):
                    if hasattr(文档, "page_content"):
                        内容 = getattr(文档, "page_content", str(文档))
                    else:
                        内容 = str(文档)

                    if 内容.strip() and len(内容.strip()) > 10:
                        上下文片段.append(f"[知识片段{i}]\n{内容.strip()}")

                智能体服务日志器.info(
                    f"✅ RAG引擎降级检索成功: {len(上下文片段)} 个文档片段"
                )
                return "\n\n".join(上下文片段)

        except ImportError:
            智能体服务日志器.warning("⚠️ RAG引擎也不可用，跳过RAG检索")
        except Exception as e:
            智能体服务日志器.error(f"❌ RAG引擎降级检索失败: {str(e)}")

        return ""

    def _构建知识库上下文文本(
        self, 检索上下文: List[Dict], 回退上下文: Dict[str, Any] = None
    ) -> str:
        """构建知识库上下文文本 - 支持智能回退上下文"""
        try:
            上下文部分 = []

            # 1. 处理检索到的知识库内容
            if 检索上下文:
                上下文部分.append("📚 === 相关知识库内容 ===")

                # 按相似度分组显示
                高相似度片段 = []
                中等相似度片段 = []

                for 上下文 in 检索上下文:
                    相似度 = 上下文.get("相似度", 0.0)
                    if 相似度 >= 0.8:
                        高相似度片段.append(上下文)
                    else:
                        中等相似度片段.append(上下文)

                # 优先显示高相似度内容
                if 高相似度片段:
                    上下文部分.append("\n🎯 高相关内容：")
                    for i, 上下文 in enumerate(高相似度片段[:3], 1):
                        self._添加知识片段(上下文部分, 上下文, f"高相关{i}")

                # 补充中等相似度内容
                if 中等相似度片段:
                    上下文部分.append("\n📖 相关参考：")
                    剩余数量 = max(0, 5 - len(高相似度片段))  # 总共最多5个片段
                    for i, 上下文 in enumerate(中等相似度片段[:剩余数量], 1):
                        self._添加知识片段(上下文部分, 上下文, f"参考{i}")

                上下文部分.append("\n📝 === 知识库内容结束 ===")

            # 2. 处理智能回退上下文
            if 回退上下文 and 回退上下文.get("回退策略"):
                上下文部分.append("\n\n🧠 === 智能辅助信息 ===")

                for 策略 in 回退上下文["回退策略"]:
                    策略类型 = 策略.get("类型", "")
                    策略内容 = 策略.get("内容", "")

                    if 策略内容:
                        if 策略类型 == "对话历史":
                            上下文部分.append(f"\n💬 {策略内容}")
                        elif 策略类型 == "查询扩展":
                            上下文部分.append(f"\n🔍 {策略内容}")
                        elif 策略类型 == "通用知识":
                            上下文部分.append(f"\n📖 {策略内容}")

                回退原因 = 回退上下文.get("回退原因", "")
                if 回退原因:
                    上下文部分.append(
                        f"\n💡 提示：{回退原因}，以上信息可能对您有帮助。"
                    )

                上下文部分.append("\n🔚 === 辅助信息结束 ===")

            # 3. 如果没有任何内容，返回空字符串
            if not 上下文部分:
                return ""

            完整文本 = "\n".join(上下文部分)

            # 长度控制 - 如果过长则截断
            最大长度 = 4000  # 约4000字符
            if len(完整文本) > 最大长度:
                截断文本 = 完整文本[:最大长度]
                # 在合适的位置截断（避免截断到文档中间）
                最后换行 = 截断文本.rfind("\n【")
                if 最后换行 > 最大长度 * 0.8:  # 至少保留80%内容
                    截断文本 = 截断文本[:最后换行]

                截断文本 += "\n\n⚠️ [内容过长已截断]"
                智能体服务日志器.warning(
                    f"⚠️ 知识库上下文过长已截断: {len(完整文本)} → {len(截断文本)} 字符"
                )
                return 截断文本

            return 完整文本

        except Exception as e:
            智能体服务日志器.warning(f"⚠️ 构建知识库上下文失败: {str(e)}")
            return ""

    def _添加知识片段(self, 上下文部分: List[str], 片段: Dict, 片段标识: str):
        """添加单个知识片段到上下文"""
        try:
            文档标题 = 片段.get("文档标题", "未知文档")
            内容 = 片段.get("内容", "").strip()
            相似度 = 片段.get("相似度", 0.0)
            来源 = 片段.get("来源", "")

            if 内容:
                # 添加片段头部信息
                头部信息 = f"\n【{片段标识}】{文档标题}"
                if 来源:
                    头部信息 += f" | {来源}"
                头部信息 += f" (相似度: {相似度:.3f})"

                上下文部分.append(头部信息)

                # 内容长度控制
                if len(内容) > 800:
                    # 截取前800字符，在句号处截断
                    截断位置 = 内容.rfind("。", 0, 800)
                    if 截断位置 > 600:  # 至少保留600字符
                        内容 = 内容[: 截断位置 + 1] + " [内容较长已截断]"
                    else:
                        内容 = 内容[:800] + "..."

                上下文部分.append(内容)

        except Exception as e:
            智能体服务日志器.warning(f"⚠️ 添加知识片段失败: {str(e)}")

    def _替换提示词变量(
        self, 提示词: str, 知识库上下文: str = "", 用户问题: str = ""
    ) -> str:
        """替换提示词中的变量 - 支持{变量名}语法，保留LangChain占位符"""
        try:
            if not 提示词:
                return ""

            # 构建变量字典 - 统一变量管理
            变量字典 = self._构建标准变量字典(知识库上下文, 用户问题, self.自定义变量)

            # 手动替换{变量名}格式的变量，保留LangChain的{input}等占位符
            结果 = 提示词
            使用的变量 = []

            for 变量名, 变量值 in 变量字典.items():
                变量模式 = f"{{{变量名}}}"
                if 变量模式 in 结果:
                    结果 = 结果.replace(变量模式, str(变量值))
                    使用的变量.append(变量名)

            # 记录变量替换信息（调试模式）
            if 智能体服务日志器.isEnabledFor(logging.DEBUG) and 使用的变量:
                智能体服务日志器.debug(
                    f"🔄 提示词变量替换: 使用了 {len(使用的变量)} 个变量: {使用的变量}"
                )

            return 结果

        except Exception as e:
            智能体服务日志器.error(f"❌ 变量替换失败: {str(e)}")
            return 提示词

    def _替换用户提示词变量(
        self, 用户提示词: str, 知识库上下文: str = "", 用户问题: str = ""
    ) -> str:
        """替换用户提示词中的变量，但保留LangChain的{input}占位符"""
        try:
            if not 用户提示词:
                return "{input}"  # 默认返回input占位符

            # 构建变量字典 - 统一变量管理
            变量字典 = self._构建标准变量字典(知识库上下文, 用户问题, self.自定义变量)

            # 手动替换{变量名}格式的变量，但跳过{input}
            结果 = 用户提示词
            使用的变量 = []

            for 变量名, 变量值 in 变量字典.items():
                # 跳过input变量，保留给LangChain处理
                if 变量名 == "input":
                    continue

                变量模式 = f"{{{变量名}}}"
                if 变量模式 in 结果:
                    结果 = 结果.replace(变量模式, str(变量值))
                    使用的变量.append(变量名)

            # 确保用户提示词中包含{input}占位符
            if "{input}" not in 结果:
                智能体服务日志器.warning("⚠️ 用户提示词中缺少{input}占位符，自动添加")
                结果 = 结果 + "\n\n用户输入：{input}"

            # 记录变量替换信息（调试模式）
            if 智能体服务日志器.isEnabledFor(logging.DEBUG) and 使用的变量:
                智能体服务日志器.debug(
                    f"🔄 用户提示词变量替换: 使用了 {len(使用的变量)} 个变量: {使用的变量}"
                )

            return 结果

        except Exception as e:
            智能体服务日志器.error(f"❌ 用户提示词变量替换失败: {str(e)}")
            return 用户提示词 if 用户提示词 else "{input}"

    def _构建标准变量字典(
        self,
        知识库上下文: str = "",
        用户问题: str = "",
        自定义变量: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """构建标准化的变量字典 - 统一变量管理入口"""
        try:
            # 基础系统变量
            变量字典 = {
                "knowledge_context": 知识库上下文 or "",
                "user_question": 用户问题 or "",
                "chat_history": "",  # 暂时为空，后续可以添加
                "聊天记录": "",  # 中文别名
                "current_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "current_date": datetime.now().strftime("%Y-%m-%d"),
                "agent_name": self.配置.智能体名称 or "智能助手",
                # 添加常用的缺失变量默认值
                "性格": "",  # 智能体性格，默认为空
                "爱好": "",  # 智能体爱好，默认为空
                "后续建议": "",  # JSON Schema字段，避免被当作变量
                "内容": "",  # JSON Schema字段，避免被当作变量
                "类型": "",  # JSON Schema字段，避免被当作变量
            }

            # 添加智能体特定变量
            if hasattr(self.配置, "智能体描述") and getattr(
                self.配置, "智能体描述", None
            ):
                变量字典["agent_description"] = getattr(self.配置, "智能体描述", "")

            # 添加配置中的自定义变量（作为默认值）
            配置变量映射 = {}
            if self.配置.自定义变量:
                for 变量 in self.配置.自定义变量:
                    if isinstance(变量, dict) and "变量名" in 变量:
                        变量名 = 变量["变量名"]
                        # 兼容两种数据结构：新版本使用"默认值"，旧版本使用"变量值"
                        默认值 = 变量.get("默认值") or 变量.get("变量值", "")
                        变量类型 = 变量.get("变量类型", "text")

                        # 验证变量名的有效性
                        if isinstance(变量名, str) and 变量名.isidentifier():
                            配置变量映射[变量名] = {
                                "默认值": str(默认值) if 默认值 is not None else "",
                                "变量类型": 变量类型,
                            }
                            # 先使用默认值
                            变量字典[变量名] = str(默认值) if 默认值 is not None else ""
                        else:
                            智能体服务日志器.warning(f"⚠️ 无效的自定义变量名: {变量名}")

            # 添加传入的自定义变量 - 优先级高于配置中的默认值
            if 自定义变量:
                for 变量名, 变量值 in 自定义变量.items():
                    if isinstance(变量名, str) and 变量名.isidentifier():
                        if 变量名 in 配置变量映射:
                            # 如果传入的值为空，使用配置中的默认值
                            if 变量值 is not None and str(变量值).strip():
                                变量字典[变量名] = str(变量值)
                                智能体服务日志器.debug(
                                    f"🔄 使用传入的自定义变量: {变量名} = {变量值}"
                                )
                            else:
                                # 传入值为空，保持使用默认值
                                智能体服务日志器.debug(
                                    f"🔄 传入变量值为空，使用默认值: {变量名} = {配置变量映射[变量名]['默认值']}"
                                )
                        else:
                            # 新的变量，直接添加（即使为空）
                            变量字典[变量名] = str(变量值) if 变量值 is not None else ""
                            智能体服务日志器.debug(
                                f"🔄 添加新的自定义变量: {变量名} = {变量值}"
                            )
                    else:
                        智能体服务日志器.warning(f"⚠️ 无效的自定义变量名: {变量名}")

            # 记录使用的变量信息
            if 配置变量映射:
                使用默认值的变量 = []
                使用传入值的变量 = []
                for 变量名 in 配置变量映射.keys():
                    if 自定义变量 and 变量名 in 自定义变量:
                        使用传入值的变量.append(变量名)
                    else:
                        使用默认值的变量.append(变量名)

                if 使用默认值的变量:
                    智能体服务日志器.info(f"📋 使用默认值的变量: {使用默认值的变量}")
                if 使用传入值的变量:
                    智能体服务日志器.info(f"📋 使用传入值的变量: {使用传入值的变量}")

            return 变量字典

        except Exception as e:
            智能体服务日志器.error(f"❌ 构建变量字典失败: {str(e)}")
            return {
                "knowledge_context": 知识库上下文 or "",
                "user_question": 用户问题 or "",
                "current_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "agent_name": "智能助手",
            }

    # 旧的输出格式处理方法已被移除，现在使用结构化输出处理器

    # 旧的JSON输出处理方法已被移除，现在使用with_structured_output方法

    # 旧的JSON Schema验证方法已被移除，现在使用Pydantic模型自动验证

    # 旧的智能修复JSON方法已被移除，现在使用with_structured_output自动处理

    # 旧的结构化输出处理方法已被移除，现在使用with_structured_output自动处理

    async def _处理列表输出(self, 原始响应: str) -> str:
        """处理列表输出"""
        try:
            if not LangChain结构化输出处理器实例.已初始化:
                await LangChain结构化输出处理器实例.初始化()

            # 解析为列表格式 - 使用默认分隔符
            # 现在自定义回复格式是JSON Schema，不包含list_separator配置
            分隔符 = "\n"

            # 使用结构化输出处理器来处理列表格式
            if 分隔符 in 原始响应:
                列表项 = [项.strip() for 项 in 原始响应.split(分隔符) if 项.strip()]
            else:
                # 如果没有分隔符，尝试按行分割
                列表项 = [项.strip() for 项 in 原始响应.split("\n") if 项.strip()]

            import json

            return json.dumps({"items": 列表项}, ensure_ascii=False, indent=2)

        except Exception as e:
            智能体服务日志器.error(f"列表输出处理失败: {str(e)}")
            return 原始响应


class LangChain智能体服务:
    """LangChain智能体统一服务 - 优雅架构设计

    架构原则：
    1. 依赖注入 - 构造时注入所有依赖
    2. 类型安全 - 完全的类型保证
    3. 单一职责 - 专注于智能体管理业务逻辑
    4. 优雅初始化 - 避免复杂的异步初始化
    """

    def __init__(self, 智能体数据层: Optional[LangChain智能体数据层] = None):
        """构造函数 - 依赖注入模式

        Args:
            智能体数据层: LangChain智能体数据层实例，如果为None则使用默认实例
        """
        # 依赖注入 - 确保智能体数据层永远不为None
        self.智能体数据层: LangChain智能体数据层 = (
            智能体数据层 or LangChain智能体数据层实例
        )
        self.已初始化 = True  # 简化初始化逻辑
        self.初始化时间 = datetime.now()

        # 运行时管理
        self.智能体实例池: Dict[str, 智能体实例] = {}
        self.用户智能体映射: Dict[int, List[str]] = {}

        # 工具管理器 - 修复工具验证问题
        self.工具管理器 = None

        智能体服务日志器.info("LangChain智能体服务创建成功")

    @classmethod
    async def 创建实例(cls) -> "LangChain智能体服务":
        """异步工厂方法 - 确保所有依赖都已初始化"""
        # 确保智能体数据层已初始化
        if not LangChain智能体数据层实例.已初始化:
            await LangChain智能体数据层实例.初始化()

        # 创建服务实例
        实例 = cls(LangChain智能体数据层实例)

        # 初始化工具管理器
        await 实例._初始化工具管理器()

        return 实例

    async def _初始化工具管理器(self):
        """初始化工具管理器"""
        try:
            from 服务.LangChain_工具管理器 import LangChain工具管理器

            # 创建工具管理器实例
            self.工具管理器 = await LangChain工具管理器.创建实例()
            智能体服务日志器.info("✅ 工具管理器初始化成功")

        except Exception as e:
            智能体服务日志器.error(f"❌ 工具管理器初始化失败: {str(e)}")
            self.工具管理器 = None

    async def _统一获取工具关联(self, 智能体id: int) -> List[Dict[str, Any]]:
        """统一的工具关联获取方法，避免重复代码"""
        try:
            if not LangChain工具数据层实例.已初始化:
                await LangChain工具数据层实例.初始化()

            工具关联列表 = await LangChain工具数据层实例.获取智能体工具关联(智能体id)
            智能体服务日志器.debug(
                f"🔧 [统一工具关联] 智能体 {智能体id} 获取到 {len(工具关联列表)} 个工具关联"
            )
            return 工具关联列表

        except Exception as e:
            智能体服务日志器.error(
                f"统一获取工具关联失败 (智能体id: {智能体id}): {str(e)}"
            )
            return []

    async def _获取工具实例(self, 工具名称: str):
        """统一的工具实例获取方法"""
        try:
            # 1. 首先尝试从工具管理器获取
            if self.工具管理器 and 工具名称 in self.工具管理器.工具注册表:
                工具实例 = self.工具管理器.工具注册表[工具名称]
                智能体服务日志器.debug(f"✅ 从工具管理器获取工具: {工具名称}")
                return 工具实例

            # 2. 尝试从内部函数包装器获取
            try:
                from 服务.LangChain_内部函数包装器 import 内部函数包装器实例

                # 确保内部函数包装器已初始化
                if not 内部函数包装器实例.已初始化:
                    await 内部函数包装器实例.初始化()

                # 获取可用工具列表
                可用工具列表 = await 内部函数包装器实例.获取可用工具列表()
                if 工具名称 in 可用工具列表:
                    工具实例 = 可用工具列表[工具名称]
                    智能体服务日志器.debug(f"✅ 从内部函数包装器获取工具: {工具名称}")
                    return 工具实例

            except Exception as e:
                智能体服务日志器.debug(f"内部函数包装器获取工具失败: {e}")

            智能体服务日志器.warning(f"⚠️ 工具未找到: {工具名称}")
            return None

        except Exception as e:
            智能体服务日志器.error(f"获取工具实例失败 {工具名称}: {str(e)}")
            return None

    def _获取工具默认测试参数(self, 工具名称: str):
        """为不同工具提供默认测试参数"""
        默认参数映射 = {
            "字符串相加": {"字符串1": "Hello", "字符串2": " World"},
            "查询用户信息": {"用户id": 1, "查询字段": "基本信息"},
            "查询用户手机号": {"用户id": 1},
            "认领达人": {"达人id": 1, "用户id": 1},
            "认领抖音达人": {"达人id": 1, "用户id": 1},
            "获取当前时间": {},
            "计算数据统计": {"数据类型": "用户统计"},
            # 数学工具
            "multiply": {"first_int": 2, "second_int": 3},
            "add": {"first_int": 5, "second_int": 3},
            "subtract": {"first_int": 10, "second_int": 4},
            "divide": {"first_int": 12, "second_int": 3},
        }

        # 返回工具特定的默认参数，如果没有定义则返回通用测试参数
        return 默认参数映射.get(工具名称, "测试")

    async def _确保数据层已初始化(self):
        """确保数据层已初始化 - Context7最佳实践"""
        # 由于使用依赖注入模式，数据层已经在构造时确保初始化
        # 这个方法保留用于向后兼容
        pass

    def _创建智能体配置(self, 智能体数据: Dict[str, Any]) -> 智能体配置:
        """统一的智能体配置创建方法 - 消除重复代码"""
        # 处理自定义回复格式
        自定义回复格式 = 智能体数据.get("自定义回复格式", None)
        if isinstance(自定义回复格式, str):
            try:
                自定义回复格式 = json.loads(自定义回复格式)
            except (json.JSONDecodeError, ValueError) as e:
                智能体服务日志器.warning(f"解析自定义回复格式失败: {str(e)}")
                自定义回复格式 = None

        # 获取智能体id
        智能体id值 = 智能体数据.get("id")

        return 智能体配置(
            用户id=智能体数据.get("用户表id", 0),
            智能体名称=智能体数据["智能体名称"],
            智能体id=智能体id值,  # 添加智能体id传递
            模型名称=智能体数据.get("模型名称", "qwen-turbo"),
            系统提示词=智能体数据.get("系统提示词", "") or "",
            用户提示词=智能体数据.get("用户提示词", "") or "",
            角色设定=智能体数据.get("角色设定", "") or "",
            行为规范=智能体数据.get("行为规范", "") or "",
            温度参数=float(智能体数据.get("温度参数", 0.7)),
            最大令牌数=int(智能体数据.get("最大令牌数", 4000)),
            记忆窗口大小=int(智能体数据.get("记忆窗口大小", 10)),
            启用rag=bool(智能体数据.get("启用rag", False)),
            工具列表=智能体数据.get("工具列表", []) or [],
            输出格式=智能体数据.get("输出格式", "text"),
            自定义回复格式=自定义回复格式,
            rag_配置=智能体数据.get("rag_配置", {}) or {},
            自定义变量=智能体数据.get("自定义变量", []) or [],
        )

    async def _确保服务已初始化(self, 服务实例, 服务名称: str) -> bool:
        """统一的服务初始化检查方法 - 消除重复代码"""
        try:
            if 服务实例 and hasattr(服务实例, "已初始化") and not 服务实例.已初始化:
                await 服务实例.初始化()
                智能体服务日志器.info(f"{服务名称}初始化完成")
            return True
        except Exception as e:
            智能体服务日志器.warning(f"{服务名称}初始化失败: {str(e)}")
            return False

    async def 根据ID或名称获取模型信息(
        self, 模型id: Optional[int] = None, 模型名称: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """根据ID或名称获取模型信息"""
        try:
            # 确保服务已初始化
            if not self.已初始化:
                await self.初始化()

            # 直接使用模型数据层实例
            if 模型id:
                模型信息 = await LangChain模型数据层实例.根据ID获取模型基本信息(模型id)
            elif 模型名称:
                模型信息 = await LangChain模型数据层实例.根据名称获取模型基本信息(
                    模型名称
                )
            else:
                return None

            return 模型信息

        except Exception as e:
            智能体服务日志器.error(f"获取模型信息失败: {str(e)}")
            return None

    async def _加载智能体配置(self):
        """从数据库加载智能体配置"""
        try:
            # 确保数据层已初始化
            await self._确保数据层已初始化()

            if not self.智能体数据层:
                return

            # 获取所有启用的智能体配置
            结果 = await self.智能体数据层.获取启用的智能体配置列表()

            if not 结果:
                智能体服务日志器.info("没有找到启用的智能体配置")
                return

            加载成功数量 = 0
            for 行 in 结果:
                try:
                    # 解析自定义回复格式 - 新数据结构
                    # 现在自定义回复格式直接是JSON Schema或None
                    自定义回复格式 = None
                    if 行.get("自定义回复格式"):
                        try:
                            自定义回复格式 = json.loads(行["自定义回复格式"])
                        except (json.JSONDecodeError, ValueError) as e:
                            智能体服务日志器.warning(
                                f"解析自定义回复格式失败: {str(e)}"
                            )
                            自定义回复格式 = None

                    # 解析RAG配置
                    rag_配置 = {
                        "检索策略": "similarity",
                        "嵌入模型id": None,
                        "相似度阈值": 0.7,
                        "最大检索数量": 5,
                    }
                    if 行.get("rag_配置"):
                        try:
                            数据库rag_配置 = json.loads(行["rag_配置"])
                            # 合并数据库配置，保留数据库中的值
                            rag_配置.update(数据库rag_配置)
                        except (json.JSONDecodeError, ValueError) as e:
                            智能体服务日志器.warning(f"解析rag_配置失败: {str(e)}")
                            pass

                    # 解析自定义变量
                    自定义变量 = []
                    if 行.get("自定义变量"):
                        try:
                            自定义变量 = json.loads(行["自定义变量"])
                        except (json.JSONDecodeError, ValueError) as e:
                            智能体服务日志器.warning(f"解析自定义变量失败: {str(e)}")
                            pass

                    # 创建智能体配置
                    配置 = 智能体配置(
                        用户id=行["用户表id"],
                        智能体名称=行["智能体名称"],
                        模型名称=行["模型名称"],
                        系统提示词=行.get("系统提示词", "") or "",
                        用户提示词=行.get("用户提示词", "") or "",
                        角色设定=行.get("角色设定", "") or "",
                        行为规范=行.get("行为规范", "") or "",
                        温度参数=float(行.get("温度参数", 0.7)),
                        最大令牌数=int(行.get("最大令牌数", 4000)),
                        记忆窗口大小=int(行.get("记忆窗口大小", 10)),
                        启用rag=bool(行.get("启用rag", False)),
                        工具列表=行.get("工具列表", []) or [],
                        输出格式=行.get("输出格式", "text"),
                        自定义回复格式=自定义回复格式,
                        rag_配置=rag_配置,
                        自定义变量=自定义变量,
                    )

                    # 创建智能体实例
                    实例ID = str(行["id"])
                    if 实例ID in self.智能体实例池:
                        # 如果已存在，先销毁
                        await self.智能体实例池[实例ID].销毁()

                    # 创建新实例
                    智能体实例对象 = 智能体实例(配置, 实例ID)
                    初始化成功 = await 智能体实例对象.初始化()

                    if 初始化成功:
                        self.智能体实例池[实例ID] = 智能体实例对象

                        # 更新用户映射
                        用户id = 配置.用户id
                        if 用户id not in self.用户智能体映射:
                            self.用户智能体映射[用户id] = []
                        if 实例ID not in self.用户智能体映射[用户id]:
                            self.用户智能体映射[用户id].append(实例ID)

                        加载成功数量 += 1

                except Exception as e:
                    智能体服务日志器.error(
                        f"加载智能体配置失败 (ID: {行.get('id')}): {str(e)}"
                    )
                    continue

            智能体服务日志器.info(
                f"智能体配置加载完成，成功加载 {加载成功数量} 个智能体"
            )

        except Exception as e:
            智能体服务日志器.error(f"加载智能体配置失败: {str(e)}")

    # ==================== 智能体配置管理 ====================

    async def 创建智能体(self, 智能体数据: Dict[str, Any]) -> Dict[str, Any]:
        """创建智能体配置和实例"""
        try:
            # 确保数据层已初始化
            await self._确保数据层已初始化()

            # 验证必要字段
            if not 智能体数据.get("智能体名称"):
                return {"success": False, "error": "智能体名称不能为空"}

            # 创建智能体配置
            智能体id = await self.智能体数据层.创建智能体配置(智能体数据)

            if not 智能体id:
                return {"success": False, "error": "智能体配置创建失败"}

            # 创建提示词配置 - 使用安全调用
            if any(
                智能体数据.get(key)
                for key in ["系统提示词", "用户提示词", "角色设定", "行为规范"]
            ):
                提示词数据 = {
                    "系统提示词": 智能体数据.get("系统提示词", ""),
                    "用户提示词": 智能体数据.get("用户提示词", ""),
                    "角色设定": 智能体数据.get("角色设定", ""),
                    "行为规范": 智能体数据.get("行为规范", ""),
                }
                await self.智能体数据层.创建提示词配置(智能体id, 提示词数据)

            # 创建运行时实例
            try:
                # 使用统一的配置创建方法
                配置 = self._创建智能体配置(智能体数据)

                # 创建智能体实例
                实例ID = str(智能体id)
                智能体实例对象 = 智能体实例(配置, 实例ID)
                初始化成功 = await 智能体实例对象.初始化()

                if 初始化成功:
                    self.智能体实例池[实例ID] = 智能体实例对象

                    # 更新用户映射
                    用户id = 配置.用户id
                    if 用户id not in self.用户智能体映射:
                        self.用户智能体映射[用户id] = []
                    if 实例ID not in self.用户智能体映射[用户id]:
                        self.用户智能体映射[用户id].append(实例ID)

            except Exception as e:
                智能体服务日志器.warning(
                    f"创建智能体实例失败，但配置已保存 (ID: {智能体id}): {str(e)}"
                )

            # 获取创建的智能体详情
            智能体详情 = await self.智能体数据层.获取智能体详情完整(智能体id)

            return {"success": True, "智能体id": 智能体id, "智能体详情": 智能体详情}

        except Exception as e:
            智能体服务日志器.error(f"创建智能体失败: {str(e)}")
            return {"success": False, "error": f"创建智能体失败: {str(e)}"}

    async def 获取智能体列表(
        self,
        页码: int = 1,
        每页数量: int = 10,
        搜索关键词: Optional[str] = None,
        状态: Optional[str] = None,
    ) -> Tuple[List[Dict[str, Any]], int]:
        """获取智能体列表"""
        try:
            # 确保数据层已初始化
            await self._确保数据层已初始化()

            结果 = await self.智能体数据层.获取智能体列表(
                页码=页码,
                每页数量=每页数量,
                搜索关键词=搜索关键词,
                状态=状态,
            )
            return 结果 if 结果 else ([], 0)
        except Exception as e:
            智能体服务日志器.error(f"获取智能体列表失败: {str(e)}")
            return [], 0

    async def 获取智能体列表详细(self, 查询参数: Dict[str, Any]) -> Dict[str, Any]:
        """获取智能体列表详细信息（管理端）"""
        try:
            # 提取查询参数
            页码 = 查询参数.get("页码", 1)
            每页数量 = 查询参数.get("每页数量", 10)

            # 构建筛选条件
            筛选条件 = {}
            if 查询参数.get("状态"):
                筛选条件["状态"] = 查询参数["状态"]
            if 查询参数.get("搜索关键词"):
                筛选条件["搜索关键词"] = 查询参数["搜索关键词"]
            if 查询参数.get("是否公开") is not None:
                筛选条件["是否公开"] = 查询参数["是否公开"]

            # 构建分页参数
            分页参数 = {"页码": 页码, "每页数量": 每页数量}

            # 调用智能体数据层获取详细列表
            智能体列表_原始, 总数量 = await self.智能体数据层.获取智能体列表详细(
                筛选条件, 分页参数
            )

            # 最终修复：强制数据塑形
            # 解决 `智能体id` 在前端为 undefined 的顽固问题。
            智能体列表_最终 = []
            if 智能体列表_原始:
                for item in 智能体列表_原始:
                    item_dict = dict(item)  # 转换为标准字典

                    # 确保 `智能体id` 字段存在且有效
                    if "智能体id" not in item_dict or item_dict["智能体id"] is None:
                        # 如果 `智能体id` 不存在或为None，尝试从 'id' 字段获取
                        if "id" in item_dict and item_dict["id"] is not None:
                            item_dict["智能体id"] = item_dict["id"]
                        else:
                            # 如果都失败，记录警告并跳过此条记录
                            智能体服务日志器.warning(
                                f"获取智能体列表时发现无效记录，已跳过: {item_dict}"
                            )
                            continue

                    智能体列表_最终.append(item_dict)

            return {
                "智能体列表": 智能体列表_最终,
                "总数量": 总数量,
                "当前页码": 页码,
                "每页数量": 每页数量,
            }

        except Exception as e:
            智能体服务日志器.error(f"获取智能体列表详细失败: {str(e)}", exc_info=True)
            return {
                "智能体列表": [],
                "总数量": 0,
                "当前页码": 查询参数.get("页码", 1),
                "每页数量": 查询参数.get("每页数量", 10),
            }

    async def 更新智能体(
        self, 智能体id: int, 更新数据: Dict[str, Any]
    ) -> Dict[str, Any]:
        """更新智能体配置 - 优雅处理保存流程"""
        try:
            智能体服务日志器.info(f"🔧 开始更新智能体配置: ID={智能体id}")
            智能体服务日志器.debug(f"📊 更新数据: {更新数据}")

            # 验证智能体是否存在
            现有智能体 = await self.智能体数据层.获取智能体详情完整(智能体id)
            if not 现有智能体:
                return {
                    "status": 状态.LangChain.智能体不存在,
                    "message": "智能体不存在，请检查智能体ID是否正确",
                    "data": None,
                }

            # 数据预处理和验证
            处理后的数据 = await self._预处理更新数据(更新数据, 现有智能体)
            if not 处理后的数据["valid"]:
                return {
                    "status": 状态.LangChain.数据验证失败,
                    "message": f"数据验证失败: {处理后的数据['error']}，请检查输入参数格式和取值范围",
                    "data": None,
                }

            # 更新基础配置
            更新成功 = await self.智能体数据层.更新智能体配置(
                智能体id, 处理后的数据["data"]
            )
            if not 更新成功:
                return {
                    "status": 状态.LangChain.智能体更新失败,
                    "message": "智能体基础配置更新失败，数据库操作异常",
                    "data": None,
                }

            # 更新提示词配置（如果提供）
            try:
                提示词字段 = ["系统提示词", "用户提示词", "角色设定", "行为规范"]
                提示词数据 = {}

                for 字段 in 提示词字段:
                    if 字段 in 处理后的数据["data"]:
                        提示词数据[字段] = 处理后的数据["data"][字段]

                if 提示词数据:
                    智能体服务日志器.info(
                        f"📝 更新提示词配置: {list(提示词数据.keys())}"
                    )
                    await self.智能体数据层.更新提示词配置(智能体id, 提示词数据)

            except Exception as e:
                智能体服务日志器.warning(f"⚠️ 提示词配置更新失败: {str(e)}，但继续执行")

            # 更新知识库关联（如果提供）
            try:
                if "知识库列表" in 处理后的数据["data"]:
                    知识库列表 = 处理后的数据["data"]["知识库列表"]
                    智能体服务日志器.info(f"📚 更新知识库关联: {知识库列表}")

                    # 调用数据层更新知识库关联
                    await self.智能体数据层.更新智能体知识库关联(智能体id, 知识库列表)
                    智能体服务日志器.info(
                        f"✅ 知识库关联更新成功: {len(知识库列表)}个知识库"
                    )

                # 处理工具关联更新
                if "工具列表" in 处理后的数据["data"]:
                    工具列表 = 处理后的数据["data"]["工具列表"]
                    智能体服务日志器.info(f"🔧 更新工具关联: {工具列表}")

                    # 调用数据层更新工具关联
                    await self.智能体数据层.更新智能体工具关联(智能体id, 工具列表)
                    智能体服务日志器.info(f"✅ 工具关联更新成功: {len(工具列表)}个工具")

            except Exception as e:
                错误信息 = str(e)
                if "知识库" in 错误信息:
                    智能体服务日志器.error(f"❌ 知识库关联更新失败: {错误信息}")
                    return {
                        "status": 500,
                        "message": f"知识库关联更新失败: {错误信息}",
                        "data": None,
                    }
                elif "工具" in 错误信息:
                    智能体服务日志器.error(f"❌ 工具关联更新失败: {错误信息}")
                    return {
                        "status": 500,
                        "message": f"工具关联更新失败: {错误信息}",
                        "data": None,
                    }
                else:
                    智能体服务日志器.error(f"❌ 关联更新失败: {错误信息}")
                    return {
                        "status": 500,
                        "message": f"关联更新失败: {错误信息}",
                        "data": None,
                    }

            # 重新加载运行时实例
            重载结果 = await self._重新加载智能体实例(智能体id)

            # 获取更新后的完整智能体信息
            更新后智能体 = await self.智能体数据层.获取智能体详情完整(智能体id)

            智能体服务日志器.info(f"✅ 智能体配置更新成功: ID={智能体id}")

            return {
                "status": 100,
                "message": "智能体配置更新成功，所有相关设置已生效",
                "data": {
                    "智能体详情": 更新后智能体,
                    "重载状态": 重载结果,
                    "更新字段": list(处理后的数据["data"].keys()),
                    "更新时间": datetime.now().isoformat(),
                },
            }

        except ValueError as e:
            智能体服务日志器.error(f"❌ 数据验证错误: {str(e)}")
            return {
                "status": 状态.LangChain.数据验证失败,
                "message": f"数据验证错误: {str(e)}，请检查输入参数",
                "data": None,
            }
        except Exception as e:
            智能体服务日志器.error(f"❌ 更新智能体失败: {str(e)}", exc_info=True)
            return {
                "status": 状态.LangChain.智能体更新失败,
                "message": f"更新智能体失败: {str(e)}，请联系管理员",
                "data": None,
            }

    async def 获取智能体关联知识库详情(self, 智能体id: int) -> Dict[str, Any]:
        """获取智能体关联的知识库详细信息"""
        try:
            智能体服务日志器.info(f"📚 获取智能体关联知识库详情: 智能体id={智能体id}")

            # 获取智能体关联的知识库列表
            关联知识库列表 = await self.智能体数据层.获取智能体知识库关联(智能体id)

            if not 关联知识库列表:
                智能体服务日志器.info(f"智能体 {智能体id} 未关联任何知识库")
                return {"知识库列表": [], "总数量": 0, "智能体id": 智能体id}

            # 获取知识库详细信息
            知识库详情列表 = []
            for 关联信息 in 关联知识库列表:
                知识id = 关联信息.get("langchain_知识库表id")
                if 知识id:
                    # 获取知识库详细信息
                    知识库详情响应 = await LangChain知识库服务实例.获取知识库详情(
                        知识id
                    )
                    if 知识库详情响应 and 知识库详情响应.get("success"):
                        知识库详情 = 知识库详情响应.get("data", {})
                        # 构建简化的知识库信息
                        简化信息 = {
                            "id": 知识库详情.get("id"),
                            "知识库名称": 知识库详情.get("知识库名称"),
                            "知识库描述": 知识库详情.get("知识库描述", ""),
                            "知识库状态": 知识库详情.get("知识库状态"),
                            "文档数量": 知识库详情.get("文档数量", 0),
                            "嵌入模型名称": 知识库详情.get("嵌入模型名称"),
                            "创建时间": 知识库详情.get("创建时间"),
                            "关联权重": 关联信息.get("权重", 1),
                            "关联状态": 关联信息.get("状态", "active"),
                        }
                        知识库详情列表.append(简化信息)

            # 返回优化的数据结构
            结果数据 = {
                "知识库列表": 知识库详情列表,
                "总数量": len(知识库详情列表),
                "智能体id": 智能体id,
            }

            智能体服务日志器.info(
                f"✅ 获取智能体关联知识库详情成功: 智能体id={智能体id}, 知识库数量={len(知识库详情列表)}"
            )
            return 结果数据

        except Exception as e:
            智能体服务日志器.error(
                f"❌ 获取智能体关联知识库详情失败: {str(e)}", exc_info=True
            )
            raise

    async def _预处理更新数据(
        self, 更新数据: Dict[str, Any], _现有智能体: Dict[str, Any]
    ) -> Dict[str, Any]:
        """预处理和验证更新数据"""
        # 标记未使用参数，保持接口一致性
        _ = _现有智能体  # 保留参数用于接口兼容性
        try:
            处理后数据 = {}

            # 基本字段验证和处理
            基本字段映射 = {
                "智能体名称": {"required": False, "type": str, "max_length": 100},
                "智能体描述": {"required": False, "type": str, "max_length": 500},
                "模型名称": {"required": False, "type": str},
                "langchain_模型配置表id": {"required": False, "type": int},
                "温度参数": {"required": False, "type": float, "min": 0.0, "max": 2.0},
                "最大令牌数": {"required": False, "type": int, "min": 1, "max": 32000},
                "记忆窗口大小": {"required": False, "type": int, "min": 1, "max": 50},
                "启用rag": {"required": False, "type": bool},
                "输出格式": {
                    "required": False,
                    "type": str,
                    "allowed_values": [
                        "text",
                        "json",
                        "list",
                        "structured",
                        "pydantic",
                    ],
                },
                "状态": {
                    "required": False,
                    "type": str,
                    "allowed_values": ["active", "inactive", "error"],
                },
                "是否公开": {"required": False, "type": (bool, int)},
                "标签": {"required": False, "type": list},
            }

            # 验证和处理基本字段
            for 字段名, 验证规则 in 基本字段映射.items():
                if 字段名 in 更新数据:
                    值 = 更新数据[字段名]
                    if 值 is not None:
                        # 类型验证
                        if not isinstance(值, 验证规则["type"]):
                            if 验证规则["type"] is float and isinstance(值, (int, str)):
                                try:
                                    值 = float(值)
                                except (ValueError, TypeError) as e:
                                    智能体服务日志器.warning(f"类型转换失败: {str(e)}")
                                    return {
                                        "valid": False,
                                        "error": f"{字段名}类型错误，需要{验证规则['type'].__name__}",
                                    }
                            elif 验证规则["type"] is int and isinstance(值, str):
                                try:
                                    值 = int(值)
                                except (ValueError, TypeError) as e:
                                    智能体服务日志器.warning(f"类型转换失败: {str(e)}")
                                    return {
                                        "valid": False,
                                        "error": f"{字段名}类型错误，需要{验证规则['type'].__name__}",
                                    }
                            elif 字段名 == "是否公开":
                                # 特殊处理是否公开字段：支持 bool 和 int 类型
                                if isinstance(值, bool):
                                    值 = 1 if 值 else 0  # 转换为数字类型
                                elif isinstance(值, int) and 值 in [0, 1]:
                                    pass  # 已经是正确的数字类型
                                else:
                                    return {
                                        "valid": False,
                                        "error": f"{字段名}必须是布尔值或0/1",
                                    }
                            else:
                                return {
                                    "valid": False,
                                    "error": f"{字段名}类型错误，需要{验证规则['type'].__name__}",
                                }

                        # 值范围验证
                        if "min" in 验证规则 and 值 < 验证规则["min"]:
                            return {
                                "valid": False,
                                "error": f"{字段名}值过小，最小值为{验证规则['min']}",
                            }
                        if "max" in 验证规则 and 值 > 验证规则["max"]:
                            return {
                                "valid": False,
                                "error": f"{字段名}值过大，最大值为{验证规则['max']}",
                            }
                        if (
                            "max_length" in 验证规则
                            and len(str(值)) > 验证规则["max_length"]
                        ):
                            return {
                                "valid": False,
                                "error": f"{字段名}长度过长，最大长度为{验证规则['max_length']}",
                            }
                        if (
                            "allowed_values" in 验证规则
                            and 值 not in 验证规则["allowed_values"]
                        ):
                            return {
                                "valid": False,
                                "error": f"{字段名}值无效，允许的值为{验证规则['allowed_values']}",
                            }

                        处理后数据[字段名] = 值

            # 特殊字段处理

            # 自定义回复格式处理 - 新数据结构
            if "自定义回复格式" in 更新数据:
                自定义回复格式 = 更新数据["自定义回复格式"]
                处理后数据["自定义回复格式"] = 自定义回复格式

            # pydantic输出模式处理 - 前端发送的字段
            if "pydantic_输出模式" in 更新数据:
                pydantic_模式 = 更新数据["pydantic_输出模式"]
                if pydantic_模式 == "with_structured_output":
                    处理后数据["输出格式"] = "structured"
                else:
                    处理后数据["输出格式"] = "text"

            # 输出模式处理 - 兼容旧字段
            if "输出模式" in 更新数据:
                输出模式 = 更新数据["输出模式"]
                处理后数据["输出格式"] = (
                    "structured" if 输出模式 == "structured" else "text"
                )

            # 知识库列表现在通过关联表管理，不再通过此处处理

            # RAG相关字段处理
            rag_字段映射 = {
                "检索策略": "检索策略",
                "嵌入模型id": "嵌入模型id",
                "相似度阈值": "相似度阈值",
                "最大检索数量": "最大检索数量",
            }

            for 前端字段, 后端字段 in rag_字段映射.items():
                if 前端字段 in 更新数据:
                    处理后数据[后端字段] = 更新数据[前端字段]

            # 自定义变量处理
            if "自定义变量" in 更新数据:
                自定义变量 = 更新数据["自定义变量"]
                if isinstance(自定义变量, list):
                    处理后数据["自定义变量"] = 自定义变量
                else:
                    return {"valid": False, "error": "自定义变量必须是数组格式"}

            # 提示词字段处理 - 确保空字符串也能正确处理
            提示词字段 = ["系统提示词", "用户提示词", "角色设定", "行为规范"]
            for 字段 in 提示词字段:
                if 字段 in 更新数据:
                    值 = 更新数据[字段]
                    # 允许空字符串，但确保是字符串类型
                    处理后数据[字段] = str(值) if 值 is not None else ""

            # 🔧 修复：处理知识库列表字段
            if "知识库列表" in 更新数据:
                知识库列表 = 更新数据["知识库列表"]
                if isinstance(知识库列表, list):
                    # 验证知识id列表的有效性
                    有效知识库列表 = []
                    for 知识id in 知识库列表:
                        try:
                            有效知识库列表.append(int(知识id))
                        except (ValueError, TypeError):
                            智能体服务日志器.warning(f"⚠️ 无效的知识id: {知识id}")
                    处理后数据["知识库列表"] = 有效知识库列表
                    智能体服务日志器.info(
                        f"📚 处理知识库列表: {len(有效知识库列表)}个知识库"
                    )
                else:
                    智能体服务日志器.warning(
                        f"⚠️ 知识库列表格式错误: {type(知识库列表)}"
                    )

            # 🔧 处理工具列表字段
            if "工具列表" in 更新数据:
                工具列表 = 更新数据["工具列表"]
                if isinstance(工具列表, list):
                    # 验证工具名称列表的有效性
                    有效工具列表 = []
                    for 工具名称 in 工具列表:
                        if isinstance(工具名称, str) and 工具名称.strip():
                            有效工具列表.append(工具名称.strip())
                        else:
                            智能体服务日志器.warning(f"⚠️ 无效的工具名称: {工具名称}")
                    处理后数据["工具列表"] = 有效工具列表
                    智能体服务日志器.info(f"🔧 处理工具列表: {len(有效工具列表)}个工具")
                else:
                    智能体服务日志器.warning(f"⚠️ 工具列表格式错误: {type(工具列表)}")

            # 🔧 处理查询优化配置字段
            if "查询优化配置" in 更新数据:
                查询优化配置 = 更新数据["查询优化配置"]
                if isinstance(查询优化配置, dict):
                    智能体服务日志器.info(f"🔧 处理查询优化配置: {查询优化配置}")
                    处理后数据["查询优化配置"] = 查询优化配置
                else:
                    智能体服务日志器.warning(
                        f"⚠️ 查询优化配置格式错误: {type(查询优化配置)}"
                    )

            智能体服务日志器.info(f"📋 数据预处理完成: {len(处理后数据)}个字段待更新")
            return {"valid": True, "data": 处理后数据}

        except Exception as e:
            智能体服务日志器.error(f"❌ 数据预处理失败: {str(e)}")
            return {"valid": False, "error": f"数据预处理失败: {str(e)}"}

    async def _重新加载智能体实例(self, 智能体id: int) -> Dict[str, Any]:
        """重新加载智能体实例"""
        try:
            实例ID = str(智能体id)
            重载结果 = {
                "重载执行": False,
                "重载成功": False,
                "实例存在": False,
                "错误信息": None,
            }

            if 实例ID in self.智能体实例池:
                重载结果["实例存在"] = True

                try:
                    await self.重新加载智能体(实例ID)
                    重载结果["重载执行"] = True
                    重载结果["重载成功"] = True
                except Exception as reload_error:
                    重载结果["重载执行"] = True
                    重载结果["重载成功"] = False
                    重载结果["错误信息"] = str(reload_error)
                    智能体服务日志器.error(f"❌ 智能体实例重新加载失败: {reload_error}")
            else:
                智能体服务日志器.info(
                    "📌 智能体实例不在实例池中，将在下次对话时自动创建"
                )

            return 重载结果

        except Exception as e:
            智能体服务日志器.error(f"❌ 重新加载智能体实例异常: {str(e)}")
            return {
                "重载执行": False,
                "重载成功": False,
                "实例存在": False,
                "错误信息": str(e),
            }

    async def 删除智能体(self, 智能体id: int) -> Dict[str, Any]:
        """删除智能体"""
        try:
            # 先销毁运行时实例
            实例ID = str(智能体id)
            if 实例ID in self.智能体实例池:
                await self.智能体实例池[实例ID].销毁()
                del self.智能体实例池[实例ID]

                # 更新用户映射
                for 智能体列表 in self.用户智能体映射.values():
                    if 实例ID in 智能体列表:
                        智能体列表.remove(实例ID)
                        break

            # 删除数据库记录
            删除成功 = await self.智能体数据层.删除智能体配置(智能体id)

            if 删除成功:
                return {"success": True, "message": "智能体删除成功"}
            else:
                return {"success": False, "error": "智能体删除失败"}

        except Exception as e:
            智能体服务日志器.error(f"删除智能体失败: {str(e)}")
            return {"success": False, "error": f"删除智能体失败: {str(e)}"}

    async def 获取智能体详情(self, 智能体id: int) -> Optional[Dict[str, Any]]:
        """获取智能体详情（包含知识库关联信息）"""
        try:
            # 确保数据层已初始化
            await self._确保数据层已初始化()

            # 获取智能体基本详情
            智能体详情 = await self.智能体数据层.获取智能体详情完整(智能体id)
            if not 智能体详情:
                return None

            # 只转换仍为smallint类型的字段
            if "是否公开" in 智能体详情:
                智能体详情["是否公开"] = bool(智能体详情["是否公开"])

            # 获取关联的知识库列表
            try:
                知识库列表 = await self._获取智能体关联知识库详情(智能体id)
                智能体详情["知识库列表"] = 知识库列表
            except Exception:
                智能体详情["知识库列表"] = []

            # 获取关联的工具列表
            try:
                工具列表 = await self._统一获取工具关联(智能体id)
                智能体详情["工具列表"] = [工具["工具名称"] for 工具 in 工具列表]
                智能体详情["工具详情"] = 工具列表
                智能体详情["启用工具调用"] = len(工具列表) > 0
            except Exception:
                智能体详情["工具列表"] = []
                智能体详情["工具详情"] = []
                智能体详情["启用工具调用"] = False

            # 解析RAG配置并提取到顶层字段
            rag_配置 = 智能体详情.get("rag_配置", {})
            if isinstance(rag_配置, dict):
                智能体详情["检索策略"] = rag_配置.get("检索策略", "similarity")
                智能体详情["相似度阈值"] = rag_配置.get("相似度阈值", 0.7)
                智能体详情["最大检索数量"] = rag_配置.get("最大检索数量", 5)
                智能体详情["查询优化配置"] = rag_配置.get("查询优化配置", {})
            else:
                智能体详情["检索策略"] = "similarity"
                智能体详情["相似度阈值"] = 0.7
                智能体详情["最大检索数量"] = 5
                智能体详情["查询优化配置"] = {}

            # 从关联的知识库中获取嵌入模型id
            智能体详情["嵌入模型id"] = None
            if 智能体详情.get("知识库列表"):
                try:
                    第一个知识库 = 智能体详情["知识库列表"][0]
                    # 处理知识id，可能是字典或整数
                    if isinstance(第一个知识库, dict):
                        知识id = 第一个知识库.get("知识id") or 第一个知识库.get("id")
                    else:
                        知识id = 第一个知识库

                    if 知识id:
                        from 数据.Postgre_异步连接池 import (
                            Postgre_异步连接池实例 as 异步连接池实例,
                        )

                        知识库查询SQL = (
                            "SELECT 嵌入模型 FROM langchain_知识库表 WHERE id = $1"
                        )
                        知识库结果 = await 异步连接池实例.执行查询(
                            知识库查询SQL, (int(知识id),)
                        )

                    if 知识库结果 and 知识库结果[0].get("嵌入模型"):
                        智能体详情["嵌入模型id"] = 知识库结果[0]["嵌入模型"]
                except Exception:
                    pass

            return 智能体详情

        except Exception as e:
            智能体服务日志器.error(f"获取智能体详情失败: {str(e)}")
            return None

    async def 验证智能体存在(self, 智能体id: int) -> Dict[str, Any]:
        """验证智能体是否存在"""
        try:
            智能体信息 = await self.智能体数据层.验证智能体存在(智能体id)

            if 智能体信息:
                return {"success": True, "智能体信息": 智能体信息}
            else:
                return {"success": False, "message": "智能体不存在"}

        except Exception as e:
            智能体服务日志器.error(f"验证智能体存在失败: {str(e)}")
            return {"success": False, "message": f"验证智能体存在失败: {str(e)}"}

    # 已删除重复方法 获取智能体配置，请使用 获取智能体详情 方法

    async def _获取智能体关联知识库详情(self, 智能体id: int) -> List[Dict[str, Any]]:
        """获取智能体关联的知识库详细信息"""
        try:
            return await LangChain智能体数据层实例.获取智能体关联知识库(智能体id)
        except Exception as e:
            智能体服务日志器.error(f"获取智能体关联知识库详情失败: {str(e)}")
            return []

    async def _获取智能体关联配置(self, 智能体id: int) -> Dict[str, Any]:
        """通过关联表获取智能体的检索配置，包括查询优化配置"""
        try:
            # 使用新的数据层方法获取完整配置
            知识库配置列表 = await LangChain智能体数据层实例.获取智能体关联知识库配置(
                智能体id
            )

            if 知识库配置列表:
                # 取第一个知识库的配置作为主要配置（按权重排序）
                主配置 = 知识库配置列表[0]

                # 构建统一的配置格式
                配置 = {
                    "权重": 主配置.get("权重", 1.0),
                    "检索策略": 主配置.get("检索策略", "similarity"),
                    "最大检索数量": 主配置.get("最大检索数量", 5),
                    "相似度阈值": 主配置.get("相似度阈值", 0.7),
                    "查询优化配置": 主配置.get("查询优化配置", {}),
                    "嵌入模型id": 主配置.get("嵌入模型id"),
                }

                智能体服务日志器.info(
                    f"🔍 智能体 {智能体id} 关联配置: 最大检索数量={配置['最大检索数量']}, "
                    f"相似度阈值={配置['相似度阈值']}, 检索策略={配置['检索策略']}, "
                    f"查询优化={'启用' if 配置['查询优化配置'].get('启用', False) else '禁用'}"
                )
                return 配置
            else:
                智能体服务日志器.warning(
                    f"⚠️ 智能体 {智能体id} 未找到关联配置，使用默认值"
                )
                return {}

        except Exception as e:
            智能体服务日志器.error(f"获取智能体关联配置失败: {str(e)}")
            return {}

    async def 验证用户智能体访问权限(
        self, 用户id: int, 智能体id: int
    ) -> Dict[str, Any]:
        """验证用户是否有权限访问指定智能体"""
        try:
            智能体服务日志器.info(
                f"🔍 验证用户访问权限: 用户{用户id} -> 智能体{智能体id}"
            )

            # 首先验证智能体是否存在
            智能体验证结果 = await self.验证智能体存在(智能体id)
            if not 智能体验证结果.get("success"):
                return {"success": False, "error": "智能体不存在"}

            智能体信息 = 智能体验证结果["智能体信息"]

            # 检查智能体状态
            智能体状态 = 智能体信息.get("状态", "停止")
            if 智能体状态 not in ["运行中", "就绪", "启用"]:
                智能体服务日志器.warning(f"⚠️ 智能体状态异常: {智能体状态}")
                return {
                    "success": False,
                    "error": f"智能体当前不可用，状态: {智能体状态}",
                }

            # 检查智能体是否为公开状态
            是否公开 = 智能体信息.get("是否公开", False)
            if 是否公开:
                智能体服务日志器.info("✅ 智能体为公开状态，用户可访问")
                return {
                    "success": True,
                    "智能体信息": 智能体信息,
                    "访问类型": "公开访问",
                }

            # 检查是否为智能体创建者
            创建者ID = 智能体信息.get("用户表id")
            if 创建者ID == 用户id:
                智能体服务日志器.info("✅ 用户为智能体创建者，可访问")
                return {
                    "success": True,
                    "智能体信息": 智能体信息,
                    "访问类型": "创建者访问",
                }

            # 检查分配权限
            分配权限检查结果 = await self.智能体数据层.检查智能体分配关系(
                用户id, 智能体id
            )
            if 分配权限检查结果:
                权限级别 = 分配权限检查结果.get("权限级别", "只读权限")
                分配状态 = 分配权限检查结果.get("状态", "禁用")

                if 分配状态 == "启用":
                    智能体服务日志器.info(
                        f"✅ 用户通过分配权限访问，权限级别: {权限级别}"
                    )
                    return {
                        "success": True,
                        "智能体信息": 智能体信息,
                        "访问类型": "分配访问",
                        "权限级别": 权限级别,
                        "分配信息": 分配权限检查结果,
                    }
                else:
                    智能体服务日志器.warning(f"⚠️ 用户有分配记录但状态为: {分配状态}")

            # 如果都不满足，则拒绝访问
            智能体服务日志器.warning("❌ 用户无权限访问智能体")
            return {"success": False, "error": "无权限访问该智能体"}

        except Exception as e:
            智能体服务日志器.error(f"验证用户智能体访问权限失败: {str(e)}")
            return {"success": False, "error": f"权限验证失败: {str(e)}"}

    async def 验证工具调用(
        self, 智能体id: int, 工具名称: str, 参数: Dict[str, Any]
    ) -> Dict[str, Any]:
        """验证智能体工具调用"""
        try:
            智能体服务日志器.info(f"验证工具调用: 智能体{智能体id}, 工具{工具名称}")

            # 1. 验证智能体是否存在
            智能体配置 = await LangChain智能体数据层实例.验证智能体存在(智能体id)
            if not 智能体配置:
                return {"success": False, "error": f"智能体 {智能体id} 不存在"}

            # 2. 检查工具是否在智能体的工具关联中 - 使用统一方法
            工具关联列表 = await self._统一获取工具关联(智能体id)
            工具关联映射 = {关联.get("工具名称"): 关联 for 关联 in 工具关联列表}

            if 工具名称 not in 工具关联映射:
                return {
                    "success": False,
                    "error": f"工具 {工具名称} 未在智能体 {智能体id} 的工具关联中",
                }

            # 3. 检查工具是否启用
            工具关联信息 = 工具关联映射[工具名称]
            if not 工具关联信息.get("启用状态", True):
                return {
                    "success": False,
                    "error": f"工具 {工具名称} 在智能体 {智能体id} 中已被禁用",
                }

            # 4. 获取工具实例 - 使用统一的工具获取逻辑
            工具实例 = await self._获取工具实例(工具名称)
            if not 工具实例:
                return {
                    "success": False,
                    "error": f"工具 {工具名称} 未在工具注册表中找到",
                }

            try:
                # 模拟工具调用 - 修复参数传递问题
                if hasattr(工具实例, "run"):
                    # LangChain工具 - 需要传递tool_input参数
                    if hasattr(工具实例, "arun"):
                        # 异步工具
                        if 参数:
                            # 将参数字典转换为字符串或直接传递
                            tool_input = (
                                参数
                                if len(参数) == 1
                                and list(参数.keys())[0] == "tool_input"
                                else 参数
                            )
                            测试结果 = await 工具实例.arun(tool_input)
                        else:
                            # 为不同工具提供默认测试参数
                            默认参数 = self._获取工具默认测试参数(工具名称)
                            测试结果 = await 工具实例.arun(默认参数)
                    else:
                        # 同步工具
                        if 参数:
                            tool_input = (
                                参数
                                if len(参数) == 1
                                and list(参数.keys())[0] == "tool_input"
                                else 参数
                            )
                            测试结果 = 工具实例.run(tool_input)
                        else:
                            # 为不同工具提供默认测试参数
                            默认参数 = self._获取工具默认测试参数(工具名称)
                            测试结果 = 工具实例.run(默认参数)
                elif callable(工具实例):
                    # 函数工具 - 直接调用
                    if asyncio.iscoroutinefunction(工具实例):
                        if 参数:
                            测试结果 = await 工具实例(**参数)
                        else:
                            # 为函数工具提供默认参数
                            默认参数 = self._获取工具默认测试参数(工具名称)
                            if isinstance(默认参数, dict):
                                测试结果 = await 工具实例(**默认参数)
                            else:
                                测试结果 = await 工具实例()
                    else:
                        if 参数:
                            测试结果 = 工具实例(**参数)
                        else:
                            # 为函数工具提供默认参数
                            默认参数 = self._获取工具默认测试参数(工具名称)
                            if isinstance(默认参数, dict):
                                测试结果 = 工具实例(**默认参数)
                            else:
                                测试结果 = 工具实例()
                else:
                    测试结果 = "工具类型验证成功"

                return {
                    "success": True,
                    "data": {
                        "智能体id": 智能体id,
                        "工具名称": 工具名称,
                        "工具类型": type(工具实例).__name__,
                        "工具描述": getattr(工具实例, "description", "")
                        or getattr(工具实例, "__doc__", ""),
                        "测试参数": 参数,
                        "测试结果": str(测试结果)[:200] + "..."
                        if len(str(测试结果)) > 200
                        else str(测试结果),
                        "验证时间": datetime.now().isoformat(),
                    },
                }

            except Exception as tool_error:
                return {
                    "success": False,
                    "error": f"工具调用测试失败: {str(tool_error)}",
                }

        except Exception as e:
            智能体服务日志器.error(f"验证工具调用失败: {str(e)}")
            return {"success": False, "error": f"验证工具调用异常: {str(e)}"}

    # ==================== 智能体运行时管理 ====================

    async def 处理对话(
        self,
        智能体id: int,
        用户消息: str,
        用户id: int,
        会话ID: str,
        结构化输出模式: bool = False,
        pydantic_模型类=None,
    ) -> Dict[str, Any]:
        """
        处理对话 - Context7优化版本，支持结构化输出，使用RAG增强
        """
        try:
            # 调用RAG增强对话方法
            对话结果 = await self.管理员RAG增强对话(
                智能体id=智能体id,
                用户表id=用户id,
                用户消息=用户消息,
                会话id=会话ID,
                测试模式=True,  # 测试模式，会保存对话记录但标记为"测试"来源
            )

            if 对话结果.get("status") == 100:
                # 成功的情况
                ai_回复 = 对话结果.get("data", {}).get("智能体回复", "")

                # 如果是结构化输出模式，尝试解析结构化数据
                结构化数据 = None
                if 结构化输出模式 and pydantic_模型类:
                    try:
                        # 尝试将AI回复解析为结构化数据
                        if ai_回复.strip().startswith("{") and ai_回复.strip().endswith(
                            "}"
                        ):
                            import json

                            json_data = json.loads(ai_回复)
                            结构化数据 = pydantic_模型类.model_validate(json_data)
                            智能体服务日志器.info("✅ 结构化输出解析成功")
                        else:
                            智能体服务日志器.warning(
                                "⚠️ AI回复不是JSON格式，无法解析为结构化数据"
                            )
                    except Exception as e:
                        智能体服务日志器.warning(f"⚠️ 结构化输出解析失败: {e}")

                return {
                    "success": True,
                    "response": ai_回复,
                    "structured_data": 结构化数据.model_dump() if 结构化数据 else None,
                    "processing_time": 对话结果.get("data", {}).get("处理时长", 0),
                }
            else:
                return {
                    "success": False,
                    "error": 对话结果.get("message", "对话处理失败"),
                }

        except Exception as e:
            智能体服务日志器.error(f"❌ 处理对话失败: {str(e)}")
            return {"success": False, "error": f"处理对话异常: {str(e)}"}

    async def 管理员RAG增强对话(
        self,
        智能体id: int,
        用户表id: int,
        用户消息: str,
        会话id: Optional[str] = None,
        测试模式: bool = False,
        自定义变量: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """管理员专用的RAG增强对话方法"""
        try:
            # 使用统一工具处理RAG增强
            from 服务.LangChain_RAG测试工具 import RAG测试工具

            # 获取智能体配置
            智能体配置 = await self.获取智能体详情(智能体id)
            RAG检索信息 = None
            增强用户消息 = 用户消息

            # 如果启用rag，执行检索增强
            if 智能体配置 and 智能体配置.get("启用rag"):
                # 获取关联配置并构建检索参数
                关联配置 = await RAG测试工具.获取智能体关联配置(智能体id)
                检索参数 = RAG测试工具.构建检索参数(关联配置)

                # 执行统一的RAG检索
                检索结果 = await RAG测试工具.执行RAG检索(智能体id, 用户消息, 检索参数)

                if 检索结果.get("success"):
                    RAG检索信息 = {
                        "检索状态": "成功",
                        "检索结果数量": 检索结果.get("结果数量", 0),
                        "知识库数量": len(智能体配置.get("知识库列表", [])),
                        "检索耗时": 检索结果.get("检索耗时", 0),
                    }

                    # 使用统一工具构建增强消息
                    检索文档列表 = 检索结果.get("检索结果", [])
                    if 检索文档列表:
                        增强用户消息 = RAG测试工具.构建RAG增强消息(
                            用户消息, 检索文档列表
                        )
                else:
                    RAG检索信息 = {
                        "检索状态": "失败",
                        "错误信息": 检索结果.get("error", "未知错误"),
                    }

            # 调用标准的智能体对话方法
            对话结果 = await self.智能体对话(
                智能体id=智能体id,
                用户表id=用户表id,
                用户消息=增强用户消息,  # 使用增强后的用户消息
                会话id=会话id,
                测试模式=测试模式,
                自定义变量=自定义变量,
            )

            # 在返回结果中添加RAG增强信息
            if 对话结果.get("status") == 100 and 对话结果.get("data"):
                对话数据 = 对话结果["data"]
                对话数据["原始用户消息"] = 用户消息
                对话数据["增强用户消息"] = (
                    增强用户消息 if 增强用户消息 != 用户消息 else None
                )
                对话数据["RAG增强"] = 增强用户消息 != 用户消息
                if RAG检索信息:
                    对话数据["RAG检索信息"] = RAG检索信息

            return 对话结果

        except Exception as e:
            智能体服务日志器.error(f"❌ 管理员RAG增强对话失败: {str(e)}")
            return {
                "status": 500,
                "message": f"管理员RAG增强对话失败: {str(e)}",
                "data": None,
            }

    async def 智能体对话(
        self,
        智能体id: int,
        用户表id: int,
        用户消息: str,
        会话id: Optional[str] = None,
        测试模式: bool = False,
        自定义变量: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """智能体对话处理"""
        try:
            实例ID = str(智能体id)

            # 获取智能体实例，如果不存在则尝试创建
            智能体实例对象 = self.智能体实例池.get(实例ID)
            if not 智能体实例对象:
                # 从数据库获取智能体配置
                智能体数据 = await self.智能体数据层.获取智能体详情完整(智能体id)
                if not 智能体数据:
                    return {
                        "status": 404,
                        "message": f"智能体配置不存在: {智能体id}",
                        "data": None,
                    }

                # 解析自定义回复格式 - 新数据结构
                # 现在自定义回复格式直接是JSON Schema或None
                自定义回复格式 = None
                原始格式 = 智能体数据.get("自定义回复格式")
                智能体服务日志器.info(
                    f"🔍 原始自定义回复格式类型: {type(原始格式)}, 内容: {原始格式}"
                )

                if 原始格式:
                    try:
                        if isinstance(原始格式, str):
                            # 如果是字符串，需要JSON解析
                            自定义回复格式 = json.loads(原始格式)
                            智能体服务日志器.info(
                                f"✅ 字符串自定义回复格式解析成功: {type(自定义回复格式)}"
                            )
                        elif isinstance(原始格式, dict):
                            # 如果已经是字典，直接使用
                            自定义回复格式 = 原始格式
                            智能体服务日志器.info(
                                f"✅ 字典自定义回复格式直接使用: {type(自定义回复格式)}"
                            )
                        else:
                            智能体服务日志器.warning(
                                f"⚠️ 未知的自定义回复格式类型: {type(原始格式)}"
                            )
                            自定义回复格式 = None
                    except Exception as e:
                        智能体服务日志器.error(f"❌ 自定义回复格式解析失败: {e}")
                        自定义回复格式 = None
                else:
                    智能体服务日志器.info(
                        "📝 智能体数据中没有自定义回复格式字段，使用None"
                    )

                # 获取工具列表并添加到智能体数据中 - 使用统一方法
                工具关联列表 = await self._统一获取工具关联(智能体id)
                工具列表 = [工具["工具名称"] for 工具 in 工具关联列表]
                智能体数据["工具列表"] = 工具列表
                智能体服务日志器.info(f"🔧 智能体对话时获取工具列表: {工具列表}")

                # 添加当前调用的用户表id到智能体数据中
                智能体数据["用户表id"] = 用户表id
                智能体服务日志器.info(f"🔧 智能体对话时设置用户表id: {用户表id}")

                # 创建智能体配置对象
                try:
                    配置 = self._创建智能体配置(智能体数据)

                    # 创建智能体实例
                    智能体实例对象 = 智能体实例(配置, 实例ID)

                    # 初始化实例
                    初始化成功 = await 智能体实例对象.初始化()
                    if not 初始化成功:
                        return {
                            "status": 500,
                            "message": f"智能体实例初始化失败: {实例ID}",
                            "data": None,
                        }

                    # 添加到实例池
                    self.智能体实例池[实例ID] = 智能体实例对象
                    智能体服务日志器.info(f"智能体实例自动创建成功: {实例ID}")

                except Exception as e:
                    智能体服务日志器.error(f"创建智能体实例失败: {str(e)}")
                    return {
                        "status": 500,
                        "message": f"创建智能体实例失败: {str(e)}",
                        "data": None,
                    }

            # 检查智能体状态（允许一定的容错性）
            if 智能体实例对象.状态 == 智能体状态.错误:
                return {
                    "status": 503,
                    "message": f"智能体状态异常: {智能体实例对象.状态.value}",
                    "data": None,
                }

            # 设置自定义变量（如果提供）
            if 自定义变量:
                智能体实例对象.自定义变量 = 自定义变量
                智能体服务日志器.info(f"🔧 设置自定义变量: {list(自定义变量.keys())}")
            else:
                智能体实例对象.自定义变量 = {}

            # 执行对话
            智能体服务日志器.info(
                f"🚀 开始执行智能体对话 - 智能体id: {智能体id}, 用户: {用户表id}"
            )
            对话结果 = await 智能体实例对象.对话(用户消息, 会话id)
            智能体服务日志器.info(
                f"📨 对话结果: success={对话结果.get('success')}, error={对话结果.get('error')}"
            )

            # 检查对话是否成功
            if not 对话结果.get("success"):
                错误信息 = 对话结果.get("error", "未知错误")
                智能体服务日志器.error(f"❌ 智能体对话失败: {错误信息}")
                return {
                    "status": 500,
                    "message": f"智能体对话失败: {错误信息}",
                    "data": None,
                }

            # 保存对话记录到数据库（包括测试模式和用户模式）
            try:
                # 提取token统计数据
                token统计数据 = {}
                if isinstance(对话结果, dict):
                    for key in [
                        "API真实输入令牌",
                        "API真实输出令牌",
                        "API真实令牌总数",
                        "代码估算输入令牌",
                        "代码估算输出令牌",
                        "代码估算令牌总数",
                    ]:
                        if key in 对话结果:
                            token统计数据[key] = 对话结果[key]

                # 根据测试模式设置调用来源
                调用来源 = "测试" if 测试模式 else "用户"

                await self._保存对话记录(
                    用户id=用户表id,
                    智能体id=智能体id,
                    会话id=会话id,
                    用户消息=用户消息,
                    智能体回复=对话结果.get("response", ""),
                    模型名称=智能体实例对象.配置.模型名称,
                    处理时长=对话结果.get("processing_time", 0),
                    算力消耗=1,  # 默认算力消耗
                    令牌消耗=对话结果.get("token_count", 0),
                    调用来源=调用来源,
                    token统计=token统计数据,
                )
            except Exception as e:
                智能体服务日志器.error(f"保存对话记录失败: {str(e)}")

            # 格式化返回结果 - 统一使用中文字段名，包含token统计数据
            返回数据 = {
                "status": 100,  # 成功状态码
                "message": "智能体对话成功",
                "data": {
                    "智能体id": 智能体id,
                    "智能体名称": 智能体实例对象.配置.智能体名称
                    if 智能体实例对象
                    else f"智能体_{智能体id}",
                    "会话ID": 会话id,
                    "用户消息": 用户消息,
                    "智能体回复": 对话结果.get("response"),
                    "处理时长": 对话结果.get("processing_time", 0),
                    "令牌消耗": 对话结果.get("token_count", 0),
                    "测试模式": 测试模式,
                    "对话时间": datetime.now().isoformat(),
                    "知识库使用": 对话结果.get("knowledge_used", False),
                    "输出格式": 对话结果.get("output_format", "text"),
                    "结构化输出": 对话结果.get("structured_output", False),
                    "输出模式": 对话结果.get("output_mode", "text"),
                    "模型名称": 对话结果.get("model_name", "unknown"),
                    "模型供应商": 对话结果.get("model_provider", "unknown"),
                    "API调用状态": 对话结果.get("api_status", "unknown"),
                    "错误信息": 对话结果.get("error"),
                    # Token统计数据
                    "API真实输入令牌": 对话结果.get("API真实输入令牌", 0),
                    "API真实输出令牌": 对话结果.get("API真实输出令牌", 0),
                    "API真实令牌总数": 对话结果.get("API真实令牌总数", 0),
                    "代码估算输入令牌": 对话结果.get("代码估算输入令牌", 0),
                    "代码估算输出令牌": 对话结果.get("代码估算输出令牌", 0),
                    "代码估算令牌总数": 对话结果.get("代码估算令牌总数", 0),
                    # 简化的调试信息
                    "调试信息": {
                        "智能体状态": str(智能体实例对象.状态)
                        if 智能体实例对象
                        else "未知",
                        "启用rag": 智能体实例对象.配置.启用rag
                        if 智能体实例对象
                        else False,
                    },
                },
            }

            智能体服务日志器.info(
                f"✅ 智能体对话完成 - 智能体id: {智能体id}, 用户: {用户表id}, 响应长度: {len(str(对话结果.get('response', '')))}"
            )

            return 返回数据
        except Exception as e:
            智能体服务日志器.error(f"智能体对话失败: {str(e)}")
            return {"status": 500, "message": f"智能体对话失败: {str(e)}", "data": None}

    async def 智能体流式对话(
        self,
        智能体id: int,
        用户表id: int,
        用户消息: str,
        会话id: Optional[str] = None,
        测试模式: bool = False,
        自定义变量: Optional[Dict[str, Any]] = None,
    ):
        """智能体流式对话处理 - 使用LCEL流式输出"""
        try:
            实例ID = str(智能体id)

            # 重新加载智能体配置
            await self.重新加载智能体(实例ID)

            # 获取智能体实例，如果不存在则尝试创建
            智能体实例对象 = self.智能体实例池.get(实例ID)
            if not 智能体实例对象:
                # 从数据库获取智能体配置
                智能体数据 = await self.智能体数据层.获取智能体详情完整(智能体id)
                if not 智能体数据:
                    yield {
                        "type": "error",
                        "content": f"智能体配置不存在: {智能体id}",
                        "metadata": {
                            "status": 404,
                            "message": f"智能体配置不存在: {智能体id}",
                        },
                    }
                    return

                # 创建智能体实例（简化版本，复用现有逻辑）
                try:
                    # 使用统一的配置创建方法
                    配置 = self._创建智能体配置(智能体数据)

                    智能体实例对象 = 智能体实例(配置, 实例ID)
                    初始化成功 = await 智能体实例对象.初始化()

                    if not 初始化成功:
                        yield {
                            "type": "error",
                            "content": "智能体实例初始化失败",
                            "metadata": {"status": 500, "message": "初始化失败"},
                        }
                        return

                    self.智能体实例池[实例ID] = 智能体实例对象
                except Exception as e:
                    yield {
                        "type": "error",
                        "content": f"创建智能体实例异常: {str(e)}",
                        "metadata": {"status": 500, "error": str(e)},
                    }
                    return

            # 检查智能体状态
            if 智能体实例对象.状态 == 智能体状态.错误:
                yield {
                    "type": "error",
                    "content": f"智能体状态异常: {智能体实例对象.状态.value}",
                    "metadata": {
                        "status": 503,
                        "agent_status": 智能体实例对象.状态.value,
                    },
                }
                return

            # 设置自定义变量
            if 自定义变量:
                智能体实例对象.自定义变量 = 自定义变量
                智能体服务日志器.info(
                    f"🔧 流式对话 - 设置自定义变量: {list(自定义变量.keys())}"
                )
            else:
                智能体实例对象.自定义变量 = {}

            # 执行流式对话
            智能体服务日志器.info(
                f"🌊 开始执行智能体流式对话 - 智能体id: {智能体id}, 用户: {用户表id}"
            )

            完整响应 = ""
            开始时间 = datetime.now()

            async for 流式块 in 智能体实例对象.流式对话(用户消息, 会话id):
                # 累积完整响应
                if 流式块.get("type") == "chunk":
                    完整响应 += 流式块.get("content", "")

                # 转发流式块，添加智能体信息
                流式块["metadata"] = 流式块.get("metadata", {})
                流式块["metadata"].update(
                    {
                        "agent_id": 智能体id,
                        "user_id": 用户表id,
                        "session_id": 会话id,
                        "test_mode": 测试模式,
                    }
                )

                yield 流式块

                # 如果是完成或错误，处理后续逻辑
                if 流式块.get("type") in ["complete", "error"]:
                    处理时长 = (datetime.now() - 开始时间).total_seconds()

                    # 保存对话记录（如果成功完成）
                    if 流式块.get("type") == "complete" and 完整响应:
                        try:
                            调用来源 = "测试" if 测试模式 else "用户"
                            token统计数据 = 流式块.get("metadata", {})

                            await self._保存对话记录(
                                用户id=用户表id,
                                智能体id=智能体id,
                                会话id=会话id,
                                用户消息=用户消息,
                                智能体回复=完整响应,
                                模型名称=智能体实例对象.配置.模型名称,
                                处理时长=处理时长,
                                算力消耗=1,
                                令牌消耗=流式块.get("metadata", {}).get(
                                    "token_count", 0
                                ),
                                调用来源=调用来源,
                                token统计=token统计数据,
                            )
                            智能体服务日志器.info("✅ 流式对话记录已保存")
                        except Exception as e:
                            智能体服务日志器.error(f"保存流式对话记录失败: {str(e)}")

                    智能体服务日志器.info(
                        f"✅ 智能体流式对话完成 - 智能体id: {智能体id}, 总耗时: {处理时长:.2f}秒"
                    )
                    break

        except Exception as e:
            智能体服务日志器.error(f"智能体流式对话失败: {str(e)}")
            yield {
                "type": "error",
                "content": f"智能体流式对话失败: {str(e)}",
                "metadata": {"status": 500, "error": str(e)},
            }

    async def _保存对话记录(
        self,
        用户id: int,
        智能体id: int,
        会话id: Optional[str],
        用户消息: str,
        智能体回复: str,
        模型名称: str,
        处理时长: float,
        算力消耗: int,
        令牌消耗: int,
        调用来源: str = "用户",
        token统计: Optional[Dict[str, int]] = None,
    ):
        """保存对话记录到数据库"""
        try:
            # 处理token统计数据
            token统计 = token统计 or {}

            对话数据 = {
                "用户表id": 用户id,
                "langchain_智能体配置表id": 智能体id,
                "会话id": 会话id
                or f"session_{用户id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "用户消息": 用户消息,
                "智能体回复": 智能体回复,
                "模型名称": 模型名称,
                "处理时长": 处理时长,
                "算力消耗": 算力消耗,
                "令牌消耗": 令牌消耗,
                "调用来源": 调用来源,
                "实际输入token": token统计.get("API真实输入令牌", 0),
                "实际输出token": token统计.get("API真实输出令牌", 0),
                "实际token总数": token统计.get("API真实令牌总数", 0),
                "估算输入token": token统计.get("代码估算输入令牌", 0),
                "估算输出token": token统计.get("代码估算输出令牌", 0),
                "估算token总数": token统计.get("代码估算令牌总数", 0),
            }

            await self.智能体数据层.创建对话记录(对话数据)
            智能体服务日志器.debug(
                f"对话记录保存成功: 用户{用户id}, 智能体{智能体id}, 来源:{调用来源}"
            )

        except Exception as e:
            智能体服务日志器.error(f"保存对话记录失败: {str(e)}")
            raise

    # ==================== 使用统计相关方法 ====================

    async def 获取使用统计数据(self, 查询参数: Dict[str, Any]) -> Dict[str, Any]:
        """获取智能体使用统计数据"""
        try:
            # 获取基础统计数据
            基础统计 = await self._获取基础统计数据(查询参数)

            # 获取趋势数据
            趋势数据 = await self._获取趋势统计数据(查询参数)

            # 获取智能体排行
            智能体排行 = await self._获取智能体使用排行(查询参数)

            # 获取用户活跃度
            用户活跃度 = await self._获取用户活跃度统计(查询参数)

            return {
                "基础统计": 基础统计,
                "趋势数据": 趋势数据,
                "智能体排行": 智能体排行,
                "用户活跃度": 用户活跃度,
                "统计时间": datetime.now().isoformat(),
            }

        except Exception as e:
            智能体服务日志器.error(f"获取使用统计数据失败: {str(e)}")
            raise

    async def 获取对话记录列表(self, 查询参数: Dict[str, Any]) -> Dict[str, Any]:
        """获取对话记录详细列表"""
        try:
            # 构建查询条件
            查询条件 = []
            查询参数_值 = []
            参数索引 = 1

            if 查询参数.get("开始日期"):
                查询条件.append("lr.创建时间 >= $1")
                查询参数_值.append(查询参数["开始日期"] + " 00:00:00")
                参数索引 += 1

            if 查询参数.get("结束日期"):
                查询条件.append("lr.创建时间 <= $1")
                查询参数_值.append(查询参数["结束日期"] + " 23:59:59")
                参数索引 += 1

            if 查询参数.get("智能体id"):
                查询条件.append("lr.langchain_智能体配置表id = $1")
                查询参数_值.append(查询参数["智能体id"])
                参数索引 += 1

            if 查询参数.get("用户id"):
                查询条件.append("lr.用户表id = $1")
                查询参数_值.append(查询参数["用户id"])
                参数索引 += 1

            where_clause = "WHERE " + " AND ".join(查询条件) if 查询条件 else ""

            # 查询总数
            计数SQL = f"""
            SELECT COUNT(*) as total
            FROM langchain_对话记录表 lr
            {where_clause}
            """

            # 使用安全的属性访问方式
            数据库连接池 = getattr(self.智能体数据层, "数据库连接池", None)
            if 数据库连接池 and hasattr(数据库连接池, "执行查询"):
                计数结果 = await 数据库连接池.执行查询(计数SQL, tuple(查询参数_值))
            else:
                计数结果 = []
            总数量 = 计数结果[0]["total"] if 计数结果 else 0

            # 查询详细记录
            页码 = 查询参数.get("页码", 1)
            每页数量 = 查询参数.get("每页数量", 20)
            偏移量 = (页码 - 1) * 每页数量

            记录SQL = f"""
            SELECT
                lr.id,
                lr.用户表id,
                lr.langchain_智能体配置表id,
                lr.会话id,
                lr.用户消息,
                lr.智能体回复,
                lr.模型名称,
                lr.处理时长,
                lr.算力消耗,
                lr.令牌消耗,
                lr.创建时间,
                lc.智能体名称,
                COALESCE(NULLIF(u.昵称, ''), u.手机号) as 用户名称
            FROM langchain_对话记录表 lr
            LEFT JOIN langchain_智能体配置表 lc ON lr.langchain_智能体配置表id = lc.id
            LEFT JOIN 用户表 u ON lr.用户表id = u.id
            {where_clause}
            ORDER BY lr.创建时间 DESC
            LIMIT ${len(查询参数_值) + 1} OFFSET ${len(查询参数_值) + 2}
            """

            # {{ AURA-X: Modify - 修复PostgreSQL参数占位符语法错误. Approval: 寸止(ID:1735372800). }}
            # {{ Source: PostgreSQL参数占位符文档 }}
            查询参数_值.extend([每页数量, 偏移量])
            记录列表 = await self.智能体数据层.数据库连接池.执行查询(
                记录SQL, tuple(查询参数_值)
            )

            return {
                "记录列表": list(记录列表) if 记录列表 else [],
                "总数量": 总数量,
                "页码": 页码,
                "每页数量": 每页数量,
                "总页数": (总数量 + 每页数量 - 1) // 每页数量,
            }

        except Exception as e:
            智能体服务日志器.error(f"获取对话记录列表失败: {str(e)}")
            raise

    async def 获取统计仪表盘数据(self) -> Dict[str, Any]:
        """获取统计仪表盘数据"""
        try:
            # 获取今日统计
            今日统计 = await self._获取今日统计()

            # 获取本周统计
            本周统计 = await self._获取本周统计()

            # 获取本月统计
            本月统计 = await self._获取本月统计()

            # 获取热门智能体
            热门智能体 = await self._获取热门智能体()

            # 获取活跃用户
            活跃用户 = await self._获取活跃用户()

            # 获取最近7天趋势
            最近趋势 = await self._获取最近趋势()

            return {
                "今日统计": 今日统计,
                "本周统计": 本周统计,
                "本月统计": 本月统计,
                "热门智能体": 热门智能体,
                "活跃用户": 活跃用户,
                "最近趋势": 最近趋势,
                "更新时间": datetime.now().isoformat(),
            }

        except Exception as e:
            智能体服务日志器.error(f"获取统计仪表盘数据失败: {str(e)}")
            raise

    # ==================== 统计数据辅助方法 ====================

    async def _获取基础统计数据(self, 查询参数: Dict[str, Any]) -> Dict[str, Any]:
        """获取基础统计数据"""
        try:
            # 构建查询条件
            查询条件 = ""
            查询参数列表 = []

            # {{ AURA-X: Modify - 修复PostgreSQL参数占位符语法错误. Approval: 寸止(ID:1735372800). }}
            # {{ Source: PostgreSQL参数占位符文档 }}
            if 查询参数.get("开始日期"):
                查询条件 += f" AND lr.创建时间 >= ${len(查询参数列表) + 1}"
                查询参数列表.append(查询参数["开始日期"] + " 00:00:00")

            if 查询参数.get("结束日期"):
                查询条件 += f" AND lr.创建时间 <= ${len(查询参数列表) + 1}"
                查询参数列表.append(查询参数["结束日期"] + " 23:59:59")

            if 查询参数.get("智能体id"):
                查询条件 += (
                    f" AND lr.langchain_智能体配置表id = ${len(查询参数列表) + 1}"
                )
                查询参数列表.append(查询参数["智能体id"])

            统计SQL = f"""
            SELECT
                COUNT(*) as 总对话数,
                COUNT(DISTINCT lr.用户表id) as 活跃用户数,
                COUNT(DISTINCT lr.langchain_智能体配置表id) as 使用智能体数,
                AVG(lr.处理时长) as 平均处理时长,
                SUM(lr.算力消耗) as 总算力消耗,
                SUM(lr.令牌消耗) as 总令牌消耗
            FROM langchain_对话记录表 lr
            WHERE 1=1 {查询条件}
            """

            结果 = await self.智能体数据层.数据库连接池.执行查询(
                统计SQL, tuple(查询参数列表)
            )

            return 结果[0] if 结果 else {}

        except Exception as e:
            智能体服务日志器.error(f"获取基础统计数据失败: {str(e)}")
            raise

    async def _获取趋势统计数据(self, 查询参数: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取趋势统计数据 - 移除日维度支持"""
        try:
            统计维度 = 查询参数.get("统计维度", "周")  # 默认使用周维度

            if 统计维度 == "周":
                分组字段 = "EXTRACT(WEEK FROM lr.创建时间)"
            else:  # 月
                分组字段 = "TO_CHAR(lr.创建时间, 'YYYY-MM')"

            # 构建查询条件
            查询条件 = ""
            查询参数列表 = []
            参数索引 = 1

            if 查询参数.get("开始日期"):
                查询条件 += " AND lr.创建时间 >= $1"
                查询参数列表.append(查询参数["开始日期"] + " 00:00:00")
                参数索引 += 1

            if 查询参数.get("结束日期"):
                查询条件 += " AND lr.创建时间 <= $1"
                查询参数列表.append(查询参数["结束日期"] + " 23:59:59")
                参数索引 += 1

            if 查询参数.get("智能体id"):
                查询条件 += " AND lr.langchain_智能体配置表id = $1"
                查询参数列表.append(查询参数["智能体id"])
                参数索引 += 1

            趋势SQL = f"""
            SELECT
                {分组字段} as 时间段,
                COUNT(*) as 对话数量,
                COUNT(DISTINCT lr.用户表id) as 用户数量,
                AVG(lr.处理时长) as 平均处理时长,
                SUM(lr.算力消耗) as 算力消耗,
                SUM(lr.令牌消耗) as 令牌消耗
            FROM langchain_对话记录表 lr
            WHERE 1=1 {查询条件}
            GROUP BY {分组字段}
            ORDER BY 时间段 DESC
            LIMIT 30
            """

            结果 = await self.智能体数据层.数据库连接池.执行查询(
                趋势SQL, tuple(查询参数列表)
            )

            return list(结果) if 结果 else []

        except Exception as e:
            智能体服务日志器.error(f"获取趋势统计数据失败: {str(e)}")
            raise

    async def _获取智能体使用排行(
        self, 查询参数: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """获取智能体使用排行"""
        try:
            # 构建查询条件
            查询条件 = ""
            查询参数列表 = []
            参数索引 = 1

            if 查询参数.get("开始日期"):
                查询条件 += " AND lr.创建时间 >= $1"
                查询参数列表.append(查询参数["开始日期"] + " 00:00:00")
                参数索引 += 1

            if 查询参数.get("结束日期"):
                查询条件 += " AND lr.创建时间 <= $1"
                查询参数列表.append(查询参数["结束日期"] + " 23:59:59")
                参数索引 += 1

            if 查询参数.get("智能体id"):
                查询条件 += " AND lr.langchain_智能体配置表id = $1"
                查询参数列表.append(查询参数["智能体id"])
                参数索引 += 1

            排行SQL = f"""
            SELECT
                lc.id as 智能体id,
                lc.智能体名称,
                COUNT(*) as 对话次数,
                COUNT(DISTINCT lr.用户表id) as 用户数量,
                AVG(lr.处理时长) as 平均处理时长,
                SUM(lr.算力消耗) as 总算力消耗,
                SUM(lr.令牌消耗) as 总令牌消耗,
                MAX(lr.创建时间) as 最后使用时间
            FROM langchain_对话记录表 lr
            LEFT JOIN langchain_智能体配置表 lc ON lr.langchain_智能体配置表id = lc.id
            WHERE 1=1 {查询条件}
            GROUP BY lc.id, lc.智能体名称
            ORDER BY 对话次数 DESC
            LIMIT 20
            """

            结果 = await self.智能体数据层.数据库连接池.执行查询(
                排行SQL, tuple(查询参数列表)
            )

            return list(结果) if 结果 else []

        except Exception as e:
            智能体服务日志器.error(f"获取智能体使用排行失败: {str(e)}")
            raise

    async def _获取用户活跃度统计(
        self, 查询参数: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """获取用户活跃度统计"""
        try:
            # 构建查询条件
            查询条件 = ""
            查询参数列表 = []
            参数索引 = 1

            if 查询参数.get("开始日期"):
                查询条件 += " AND lr.创建时间 >= $1"
                查询参数列表.append(查询参数["开始日期"] + " 00:00:00")
                参数索引 += 1

            if 查询参数.get("结束日期"):
                查询条件 += " AND lr.创建时间 <= $1"
                查询参数列表.append(查询参数["结束日期"] + " 23:59:59")
                参数索引 += 1

            if 查询参数.get("智能体id"):
                查询条件 += " AND lr.langchain_智能体配置表id = $1"
                查询参数列表.append(查询参数["智能体id"])
                参数索引 += 1

            活跃度SQL = f"""
            SELECT
                u.id as 用户id,
                COALESCE(NULLIF(u.昵称, ''), u.手机号) as 用户名,
                COUNT(*) as 对话次数,
                COUNT(DISTINCT lr.langchain_智能体配置表id) as 使用智能体数,
                SUM(lr.算力消耗) as 总算力消耗,
                SUM(lr.令牌消耗) as 总令牌消耗,
                MAX(lr.创建时间) as 最后活跃时间
            FROM langchain_对话记录表 lr
            LEFT JOIN 用户表 u ON lr.用户表id = u.id
            WHERE 1=1 {查询条件}
            GROUP BY u.id, COALESCE(NULLIF(u.昵称, ''), u.手机号)
            ORDER BY 对话次数 DESC
            LIMIT 20
            """

            结果 = await self.智能体数据层.数据库连接池.执行查询(
                活跃度SQL, tuple(查询参数列表)
            )

            return list(结果) if 结果 else []

        except Exception as e:
            智能体服务日志器.error(f"获取用户活跃度统计失败: {str(e)}")
            raise

    async def _获取今日统计(self) -> Dict[str, Any]:
        """获取今日统计"""
        try:
            今日SQL = """
            SELECT
                COUNT(*) as 今日对话数,
                COUNT(DISTINCT 用户表id) as 今日活跃用户,
                COUNT(DISTINCT langchain_智能体配置表id) as 今日使用智能体,
                SUM(算力消耗) as 今日算力消耗,
                SUM(令牌消耗) as 今日令牌消耗
            FROM langchain_对话记录表
            WHERE DATE(创建时间) = CURRENT_DATE
            """

            结果 = await self.智能体数据层.数据库连接池.执行查询(今日SQL)
            return 结果[0] if 结果 else {}

        except Exception as e:
            智能体服务日志器.error(f"获取今日统计失败: {str(e)}")
            raise

    async def _获取本周统计(self) -> Dict[str, Any]:
        """获取本周统计"""
        try:
            本周SQL = """
            SELECT
                COUNT(*) as 本周对话数,
                COUNT(DISTINCT 用户表id) as 本周活跃用户,
                COUNT(DISTINCT langchain_智能体配置表id) as 本周使用智能体,
                SUM(算力消耗) as 本周算力消耗,
                SUM(令牌消耗) as 本周令牌消耗
            FROM langchain_对话记录表
            WHERE EXTRACT(WEEK FROM 创建时间) = EXTRACT(WEEK FROM NOW())
            """

            结果 = await self.智能体数据层.数据库连接池.执行查询(本周SQL)
            return 结果[0] if 结果 else {}

        except Exception as e:
            智能体服务日志器.error(f"获取本周统计失败: {str(e)}")
            raise

    async def _获取本月统计(self) -> Dict[str, Any]:
        """获取本月统计"""
        try:
            本月SQL = """
            SELECT
                COUNT(*) as 本月对话数,
                COUNT(DISTINCT 用户表id) as 本月活跃用户,
                COUNT(DISTINCT langchain_智能体配置表id) as 本月使用智能体,
                SUM(算力消耗) as 本月算力消耗,
                SUM(令牌消耗) as 本月令牌消耗
            FROM langchain_对话记录表
            WHERE EXTRACT(YEAR FROM 创建时间) = EXTRACT(YEAR FROM NOW()) AND EXTRACT(MONTH FROM 创建时间) = EXTRACT(MONTH FROM NOW())
            """

            结果 = await self.智能体数据层.数据库连接池.执行查询(本月SQL)
            return 结果[0] if 结果 else {}

        except Exception as e:
            智能体服务日志器.error(f"获取本月统计失败: {str(e)}")
            raise

    async def _获取热门智能体(self) -> List[Dict[str, Any]]:
        """获取热门智能体（最近7天）"""
        try:
            热门SQL = """
            SELECT
                lc.id as 智能体id,
                lc.智能体名称,
                COUNT(*) as 对话次数,
                COUNT(DISTINCT lr.用户表id) as 用户数量
            FROM langchain_对话记录表 lr
            LEFT JOIN langchain_智能体配置表 lc ON lr.langchain_智能体配置表id = lc.id
            WHERE lr.创建时间 >= CURRENT_TIMESTAMP - INTERVAL '7 days'
            GROUP BY lc.id, lc.智能体名称
            ORDER BY 对话次数 DESC
            LIMIT 10
            """

            结果 = await self.智能体数据层.数据库连接池.执行查询(热门SQL)
            return list(结果) if 结果 else []

        except Exception as e:
            智能体服务日志器.error(f"获取热门智能体失败: {str(e)}")
            raise

    async def _获取活跃用户(self) -> List[Dict[str, Any]]:
        """获取活跃用户（最近7天）"""
        try:
            活跃SQL = """
            SELECT
                u.id as 用户id,
                u.昵称,
                COUNT(*) as 对话次数,
                COUNT(DISTINCT lr.langchain_智能体配置表id) as 使用智能体数
            FROM langchain_对话记录表 lr
            LEFT JOIN 用户表 u ON lr.用户表id = u.id
            WHERE lr.创建时间 >= CURRENT_TIMESTAMP - INTERVAL '7 days'
            GROUP BY u.id, u.昵称
            ORDER BY 对话次数 DESC
            LIMIT 10
            """

            结果 = await self.智能体数据层.数据库连接池.执行查询(活跃SQL)
            return list(结果) if 结果 else []

        except Exception as e:
            智能体服务日志器.error(f"获取活跃用户失败: {str(e)}")
            raise

    async def _获取最近趋势(self) -> List[Dict[str, Any]]:
        """获取最近7天趋势"""
        try:
            趋势SQL = """
            SELECT
                DATE(创建时间) as 日期,
                COUNT(*) as 对话数量,
                COUNT(DISTINCT 用户表id) as 用户数量,
                COUNT(DISTINCT langchain_智能体配置表id) as 智能体数量
            FROM langchain_对话记录表
            WHERE 创建时间 >= CURRENT_TIMESTAMP - INTERVAL '7 days'
            GROUP BY DATE(创建时间)
            ORDER BY 日期 ASC
            """

            结果 = await self.智能体数据层.数据库连接池.执行查询(趋势SQL)
            return list(结果) if 结果 else []

        except Exception as e:
            智能体服务日志器.error(f"获取最近趋势失败: {str(e)}")
            raise

    async def 重新加载智能体(self, 智能体id: Optional[str] = None) -> bool:
        """重新加载智能体配置"""
        try:
            if 智能体id:
                # 重新加载特定智能体
                if 智能体id in self.智能体实例池:
                    # 销毁旧实例
                    await self.智能体实例池[智能体id].销毁()
                    del self.智能体实例池[智能体id]

                # 从数据库重新加载配置
                行 = await self.智能体数据层.获取智能体详情完整(int(智能体id))

                if 行:
                    # 解析自定义回复格式 - 新数据结构
                    # 现在自定义回复格式直接是JSON Schema或None
                    自定义回复格式 = None
                    原始格式 = 行.get("自定义回复格式")
                    智能体服务日志器.info(
                        f"🔍 重新加载时原始自定义回复格式类型: {type(原始格式)}, 内容: {原始格式}"
                    )

                    if 原始格式:
                        try:
                            if isinstance(原始格式, str):
                                # 如果是字符串，需要JSON解析
                                自定义回复格式 = json.loads(原始格式)
                                智能体服务日志器.info(
                                    f"✅ 重新加载时字符串自定义回复格式解析成功: {type(自定义回复格式)}"
                                )
                            elif isinstance(原始格式, dict):
                                # 如果已经是字典，直接使用
                                自定义回复格式 = 原始格式
                                智能体服务日志器.info(
                                    f"✅ 重新加载时字典自定义回复格式直接使用: {type(自定义回复格式)}"
                                )
                            else:
                                智能体服务日志器.warning(
                                    f"⚠️ 重新加载时未知的自定义回复格式类型: {type(原始格式)}"
                                )
                                自定义回复格式 = None
                        except Exception as e:
                            智能体服务日志器.error(
                                f"❌ 重新加载时自定义回复格式解析失败: {e}"
                            )
                            自定义回复格式 = None
                    else:
                        智能体服务日志器.info(
                            "📝 重新加载时智能体数据中没有自定义回复格式字段，使用None"
                        )

                    # 获取工具列表 - 使用统一方法
                    工具关联列表 = await self._统一获取工具关联(int(智能体id))
                    工具列表 = [工具["工具名称"] for 工具 in 工具关联列表]
                    智能体服务日志器.info(f"🔧 重新加载时获取工具列表: {工具列表}")

                    # 创建智能体配置
                    配置 = 智能体配置(
                        用户id=行["用户表id"],
                        智能体名称=行["智能体名称"],
                        智能体id=int(智能体id),  # 添加智能体id传递
                        模型名称=行["模型名称"],
                        系统提示词=行.get("系统提示词", "") or "",
                        用户提示词=行.get("用户提示词", "") or "",
                        角色设定=行.get("角色设定", "") or "",
                        行为规范=行.get("行为规范", "") or "",
                        温度参数=float(行.get("温度参数", 0.7)),
                        最大令牌数=int(行.get("最大令牌数", 4000)),
                        记忆窗口大小=int(行.get("记忆窗口大小", 10)),
                        启用rag=bool(行.get("启用rag", False)),
                        工具列表=工具列表,  # 添加工具列表
                        输出格式=行.get("输出格式", "text"),
                        自定义回复格式=自定义回复格式,
                    )

                    # 创建新实例
                    智能体实例对象 = 智能体实例(配置, 智能体id)
                    初始化成功 = await 智能体实例对象.初始化()

                    if 初始化成功:
                        self.智能体实例池[智能体id] = 智能体实例对象
                        智能体服务日志器.info(f"智能体重新加载成功: {智能体id}")
                        return True

                智能体服务日志器.warning(f"智能体重新加载失败: {智能体id}")
                return False
            else:
                # 重新加载所有智能体
                await self._加载智能体配置()
                智能体服务日志器.info("所有智能体重新加载完成")
                return True

        except Exception as e:
            智能体服务日志器.error(f"重新加载智能体失败: {str(e)}")
            return False

    # ==================== 用户智能体关联管理 ====================

    async def 分配智能体给用户(
        self, 智能体id: int, 用户id列表: List[int]
    ) -> Dict[str, Any]:
        """分配智能体给用户"""
        try:
            return await self.智能体数据层.分配智能体给用户详细(智能体id, 用户id列表)
        except Exception as e:
            智能体服务日志器.error(f"分配智能体给用户失败: {str(e)}")
            return {"success": False, "error": f"分配智能体给用户失败: {str(e)}"}

    async def 分配智能体给用户详细(
        self,
        智能体id: int,
        用户id列表: List[int],
        权限级别: str = "读写权限",
        分配类型: str = "个人分配",
        备注: str = "",
    ) -> Dict[str, Any]:
        """分配智能体给用户（详细版本，支持权限级别和分配类型）"""
        try:
            return await self.智能体数据层.分配智能体给用户详细(
                智能体id, 用户id列表, 权限级别, 分配类型, 备注
            )
        except Exception as e:
            智能体服务日志器.error(f"分配智能体给用户详细失败: {str(e)}")
            return {"success": False, "error": f"分配智能体给用户详细失败: {str(e)}"}

    async def 撤销用户智能体分配(
        self, 智能体id: int, 用户id列表: List[int]
    ) -> Dict[str, Any]:
        """撤销用户智能体分配"""
        try:
            return await self.智能体数据层.取消智能体分配批量(智能体id, 用户id列表)
        except Exception as e:
            智能体服务日志器.error(f"撤销用户智能体分配失败: {str(e)}")
            return {"success": False, "error": f"撤销用户智能体分配失败: {str(e)}"}

    async def 撤销单个智能体分配(self, 分配ID: int) -> Dict[str, Any]:
        """撤销单个智能体分配"""
        try:
            return await self.智能体数据层.撤销单个智能体分配(分配ID)
        except Exception as e:
            智能体服务日志器.error(f"撤销单个智能体分配失败: {str(e)}")
            return {"success": False, "error": f"撤销单个智能体分配失败: {str(e)}"}

    async def 获取智能体分配用户详细(
        self, 智能体id: int, 页码: int = 1, 每页数量: int = 10
    ) -> Dict[str, Any]:
        """获取智能体分配的用户详细信息"""
        try:
            分页参数 = {"页码": 页码, "每页数量": 每页数量}
            用户列表, 总数量 = await self.智能体数据层.获取智能体分配用户详细(
                智能体id, 分页参数
            )
            return {
                "success": True,
                "data": {
                    "用户列表": 用户列表,
                    "总数量": 总数量,
                    "当前页码": 页码,
                    "每页数量": 每页数量,
                },
            }
        except Exception as e:
            智能体服务日志器.error(f"获取智能体分配用户详细失败: {str(e)}")
            return {"success": False, "error": f"获取智能体分配用户详细失败: {str(e)}"}

    async def 获取用户智能体分配列表详细(
        self, 查询参数: Dict[str, Any]
    ) -> Dict[str, Any]:
        """获取用户智能体分配列表详细信息"""
        try:
            # 构建分页参数
            分页参数 = {
                "页码": 查询参数.get("页码", 1),
                "每页数量": 查询参数.get("每页数量", 10),
            }

            # 调用数据层获取分配列表
            分配列表, 总数量 = await self.智能体数据层.获取用户智能体分配列表详细(
                用户id=查询参数.get("用户id"),
                智能体id=查询参数.get("智能体id"),
                分页参数=分页参数,
            )

            return {
                "分配列表": 分配列表,
                "总数量": 总数量,
                "当前页码": 分页参数["页码"],
                "每页数量": 分页参数["每页数量"],
            }
        except Exception as e:
            智能体服务日志器.error(f"获取用户智能体分配列表详细失败: {str(e)}")
            return {"分配列表": [], "总数量": 0, "当前页码": 1, "每页数量": 10}

    async def 搜索用户(
        self, 搜索关键词: str, 页码: int = 1, 每页数量: int = 10
    ) -> Dict[str, Any]:
        """搜索用户用于智能体分配"""
        try:
            # 使用统一的PostgreSQL搜索用户函数
            from 数据.用户 import 搜索用户

            分页参数 = {"页码": 页码, "每页数量": 每页数量}
            # 只搜索昵称和手机号，过滤掉已删除的用户
            用户列表, 总数量 = await 搜索用户(
                搜索关键词, 分页参数, 搜索字段=["昵称", "手机号"], 状态过滤="已删除"
            )

            # 转换数据格式以保持兼容性
            格式化用户列表 = []
            for 用户 in 用户列表:
                格式化用户列表.append(
                    {
                        "用户id": 用户["id"],
                        "昵称": 用户["昵称"],
                        "手机号": 用户["手机号"],
                        "状态": 用户["状态"],
                        "创建时间": 用户["created_at"],
                    }
                )
            return {
                "success": True,
                "data": {
                    "用户列表": 格式化用户列表,
                    "总数量": 总数量,
                    "当前页码": 页码,
                    "每页数量": 每页数量,
                },
            }
        except Exception as e:
            智能体服务日志器.error(f"搜索用户失败: {str(e)}")
            return {"success": False, "error": f"搜索用户失败: {str(e)}"}

    async def 获取用户可用智能体列表(
        self, 用户id: int, 查询参数: Dict[str, Any]
    ) -> Dict[str, Any]:
        """获取用户可用的智能体列表"""
        try:
            # 从查询参数中提取分页信息
            页码 = 查询参数.get("页码", 1)
            每页数量 = 查询参数.get("每页数量", 10)
            搜索关键词 = 查询参数.get("搜索关键词")

            # 构建数据层查询参数
            数据层查询参数 = {
                "页码": 页码,
                "每页数量": 每页数量,
                "搜索关键词": 搜索关键词,
            }

            智能体列表, 总数量 = await self.智能体数据层.获取用户可用智能体列表(
                用户id, 数据层查询参数
            )

            return {
                "智能体列表": 智能体列表,
                "总数量": 总数量,
                "当前页码": 页码,
                "每页数量": 每页数量,
            }
        except Exception as e:
            智能体服务日志器.error(f"获取用户可用智能体列表失败: {str(e)}")
            return {"智能体列表": [], "总数量": 0, "当前页码": 1, "每页数量": 10}

    # ==================== 智能体统计和监控 ====================

    async def 获取智能体统计信息(self) -> Dict[str, Any]:
        """获取智能体统计信息"""
        try:
            # 数据库统计
            数据库统计 = await self.智能体数据层.获取智能体统计数据()

            # 运行时统计
            运行时统计 = {
                "运行中实例数": len(
                    [
                        i
                        for i in self.智能体实例池.values()
                        if i.状态 == 智能体状态.运行中
                    ]
                ),
                "总实例数": len(self.智能体实例池),
                "活跃用户数": len(self.用户智能体映射),
                "实例状态分布": {},
            }

            # 统计实例状态分布
            状态统计 = {}
            for 实例 in self.智能体实例池.values():
                状态 = 实例.状态.value
                状态统计[状态] = 状态统计.get(状态, 0) + 1
            运行时统计["实例状态分布"] = 状态统计

            return {
                "success": True,
                "数据库统计": 数据库统计,
                "运行时统计": 运行时统计,
                "统计时间": datetime.now().isoformat(),
            }

        except Exception as e:
            智能体服务日志器.error(f"获取智能体统计信息失败: {str(e)}")
            return {"success": False, "error": f"获取智能体统计信息失败: {str(e)}"}

    def 获取系统统计(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        try:
            总对话次数 = sum(实例.对话次数 for 实例 in self.智能体实例池.values())
            总令牌消耗 = sum(实例.总令牌消耗 for 实例 in self.智能体实例池.values())
            总错误次数 = sum(实例.错误次数 for 实例 in self.智能体实例池.values())

            return {
                "智能体实例数": len(self.智能体实例池),
                "活跃用户数": len(self.用户智能体映射),
                "总对话次数": 总对话次数,
                "总令牌消耗": 总令牌消耗,
                "总错误次数": 总错误次数,
                "平均对话次数": 总对话次数 / len(self.智能体实例池)
                if self.智能体实例池
                else 0,
                "错误率": 总错误次数 / max(总对话次数, 1) * 100,
            }
        except Exception as e:
            智能体服务日志器.error(f"获取系统统计失败: {str(e)}")
            return {}

    async def 获取智能体实例状态(
        self, 智能体id: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取智能体实例状态"""
        try:
            if 智能体id:
                # 获取特定智能体实例状态
                实例 = self.智能体实例池.get(智能体id)
                if 实例:
                    return {"success": True, "智能体状态": 实例.获取统计信息()}
                else:
                    return {"success": False, "error": "智能体实例不存在"}
            else:
                # 获取所有智能体实例状态
                所有状态 = {}
                for 实例ID, 实例 in self.智能体实例池.items():
                    所有状态[实例ID] = 实例.获取统计信息()

                return {
                    "success": True,
                    "所有智能体状态": 所有状态,
                    "总数": len(所有状态),
                }

        except Exception as e:
            智能体服务日志器.error(f"获取智能体实例状态失败: {str(e)}")
            return {"success": False, "error": f"获取智能体实例状态失败: {str(e)}"}

    # ==================== 委托方法（兼容性） ====================

    async def 获取向量模型列表带统计(self) -> Dict[str, Any]:
        """获取向量模型列表带统计（委托给模型管理器）"""
        try:
            if not LangChain模型管理器实例.已初始化:
                await LangChain模型管理器实例.初始化()
            return await LangChain模型管理器实例.获取向量模型列表带统计()
        except Exception as e:
            智能体服务日志器.error(f"获取向量模型列表带统计失败: {str(e)}")
            return {"success": False, "error": f"获取向量模型列表带统计失败: {str(e)}"}

    async def 获取支持的文件格式(self) -> Dict[str, Any]:
        """获取支持的文件格式"""
        try:
            格式列表 = LangChain文档处理器实例.获取支持的文件格式列表()

            # 统计格式数量
            格式统计 = {
                "总格式数": len(格式列表),
                "文档类型": len([f for f in 格式列表 if f["类型"] == "document"]),
                "表格类型": len([f for f in 格式列表 if f["类型"] == "spreadsheet"]),
                "文本类型": len([f for f in 格式列表 if f["类型"] == "text"]),
                "其他类型": len(
                    [
                        f
                        for f in 格式列表
                        if f["类型"] not in ["document", "spreadsheet", "text"]
                    ]
                ),
            }

            return {"success": True, "支持的格式列表": 格式列表, "格式统计": 格式统计}
        except Exception as e:
            智能体服务日志器.error(f"获取支持的文件格式失败: {str(e)}")
            return {"success": False, "error": f"获取支持的文件格式失败: {str(e)}"}

    async def 上传文档到知识库(
        self, 知识id: int, 文件路径: str, 文件名: str
    ) -> Dict[str, Any]:
        """上传文档到知识库"""
        try:
            # 使用文档处理器处理文档
            await self._确保服务已初始化(LangChain文档处理器实例, "文档处理器")

            处理结果 = await LangChain文档处理器实例.处理文档文件(
                文件路径, 知识id, 文件名
            )

            if 处理结果.get("success"):
                智能体服务日志器.info(
                    f"上传文档到知识库成功: {文件名} -> 知识库 {知识id}"
                )
                return {
                    "success": True,
                    "文档id": 处理结果.get("文档UUID"),
                    "文件名": 文件名,
                    "message": "文档上传成功",
                }
            else:
                return {
                    "success": False,
                    "error": 处理结果.get("error", "文档上传失败"),
                }

        except Exception as e:
            智能体服务日志器.error(f"上传文档到知识库失败: {str(e)}")
            return {"success": False, "error": f"上传文档失败: {str(e)}"}

    async def 向量化知识库(self, 知识id: int) -> Dict[str, Any]:
        """向量化知识库"""
        try:
            # 这里应该启动向量化任务
            # 临时返回成功状态
            智能体服务日志器.info(f"开始向量化知识库: {知识id}")
            return {
                "success": True,
                "任务id": f"vectorize_{知识id}_{int(time.time())}",
                "message": "向量化任务已启动",
            }

        except Exception as e:
            智能体服务日志器.error(f"向量化知识库失败: {str(e)}")
            return {"success": False, "error": f"向量化知识库失败: {str(e)}"}

    async def 获取向量化状态(self, 知识id: int) -> Dict[str, Any]:
        """获取向量化状态"""
        try:
            # 临时返回完成状态
            return {
                "success": True,
                "状态": "已完成",
                "进度": 100,
                "message": f"知识库 {知识id} 向量化状态",
            }

        except Exception as e:
            智能体服务日志器.error(f"获取向量化状态失败: {str(e)}")
            return {"success": False, "error": f"获取向量化状态失败: {str(e)}"}

    async def 更新智能体启用状态(self, 智能体id: int, 是否启用: bool) -> Dict[str, Any]:
        """更新智能体启用状态"""
        try:
            return await self.智能体数据层.更新智能体启用状态(智能体id, 是否启用)
        except Exception as e:
            智能体服务日志器.error(f"更新智能体启用状态失败: {str(e)}")
            return {"success": False, "error": f"更新智能体启用状态失败: {str(e)}"}

    async def _记录工具调用日志(
        self, 工具名称: str, 执行状态: str, 错误信息: Optional[str] = None
    ):
        """记录内部函数工具调用日志"""
        try:
            await self._确保服务已初始化(LangChain工具数据层实例, "LangChain工具数据层")

            成功 = 执行状态 == "成功" or 错误信息 is None
            await LangChain工具数据层实例.记录工具调用(工具名称, 成功=成功)

        except Exception as e:
            错误日志器.error(f"记录工具调用日志失败: {str(e)}")


# 创建全局智能体服务实例
LangChain智能体服务实例 = LangChain智能体服务()
