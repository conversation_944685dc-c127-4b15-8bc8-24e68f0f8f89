"""
LangChain 内部函数包装器 - 简化版本
基于FastAPI依赖注入的简化内部函数调用系统

主要功能：
1. 直接使用FastAPI的@tool装饰器
2. 简化的用户id注入机制
3. 统一的错误处理和日志记录
4. 直接的数据库关联

作者：系统
创建时间：2024年
简化时间：2024年
"""

import asyncio
import json
import logging
import threading
from functools import wraps
from typing import Any, Callable, Dict

# 设置日志
内部函数包装器日志器 = logging.getLogger("LangChain.内部函数包装器")

# 线程本地存储，用于传递当前用户ID上下文
_thread_local = threading.local()

# LangChain工具组件
LANGCHAIN_AVAILABLE = False
try:
    from langchain_core.tools import tool  # type: ignore

    LANGCHAIN_AVAILABLE = True
except ImportError:
    # 创建占位符
    def tool(func):  # type: ignore
        return func


# 上下文管理函数
def 设置当前用户id(用户id: int):
    """设置当前线程的用户ID上下文"""
    _thread_local.current_user_id = 用户id


def 获取当前用户id() -> int | None:
    """获取当前线程的用户ID上下文"""
    return getattr(_thread_local, 'current_user_id', None)


def 清除当前用户id():
    """清除当前线程的用户ID上下文"""
    if hasattr(_thread_local, 'current_user_id'):
        delattr(_thread_local, 'current_user_id')


# 简化的工具装饰器
def 简化内部函数工具(名称: str, 描述: str):
    """简化的内部函数工具装饰器，直接使用LangChain的@tool"""

    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                内部函数包装器日志器.info(f"🔧 调用内部函数工具: {名称}")

                # 如果函数是异步的，直接调用
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)

                内部函数包装器日志器.debug(f"✅ 工具调用成功: {名称}")
                return result

            except Exception as e:
                内部函数包装器日志器.error(f"❌ 内部函数工具调用失败 {名称}: {str(e)}")
                return f"调用失败: {str(e)}"

        # 设置函数属性
        wrapper.__doc__ = 描述
        wrapper.__name__ = 名称

        # 使用LangChain的@tool装饰器（如果可用）
        return tool(wrapper) if LANGCHAIN_AVAILABLE else wrapper

    return decorator


# 移除复杂的用户id注入包装器类，使用更简单的方式


def 创建用户工具(原始工具, 用户id: int):
    """创建用户专用工具，注入用户ID并返回LangChain StructuredTool对象"""
    from langchain_core.tools import StructuredTool

    # 如果原始工具是StructuredTool，创建一个新的实例
    if hasattr(原始工具, "invoke") and hasattr(原始工具, "args_schema"):
        # 创建包装函数，自动注入用户ID并调用原始工具
        async def 用户工具包装函数(**kwargs):
            # 自动注入用户id到参数中
            kwargs["用户id"] = 用户id
            try:
                # 直接调用原始工具的ainvoke方法（异步）
                return await 原始工具.ainvoke(kwargs)
            except Exception as e:
                内部函数包装器日志器.error(f"❌ 用户工具调用失败: {str(e)}")
                return f"调用失败: {str(e)}"

        # 创建新的StructuredTool，复制原始工具的属性
        用户工具 = StructuredTool.from_function(
            func=用户工具包装函数,
            name=原始工具.name,
            description=原始工具.description,
            args_schema=原始工具.args_schema,
            return_direct=getattr(原始工具, "return_direct", False),
            coroutine=用户工具包装函数,  # 指定异步函数
        )

        return 用户工具
    else:
        # 如果不是StructuredTool，返回包装函数（向后兼容）
        async def 用户工具包装器(*args, **kwargs):
            kwargs["用户id"] = 用户id
            try:
                if hasattr(原始工具, "arun"):
                    return await 原始工具.arun(*args, **kwargs)
                elif hasattr(原始工具, "run"):
                    return 原始工具.run(*args, **kwargs)
                else:
                    if asyncio.iscoroutinefunction(原始工具):
                        return await 原始工具(*args, **kwargs)
                    else:
                        return 原始工具(*args, **kwargs)
            except Exception as e:
                内部函数包装器日志器.error(f"❌ 用户工具调用失败: {str(e)}")
                return f"调用失败: {str(e)}"

        用户工具包装器.name = getattr(原始工具, "name", "未知工具")
        用户工具包装器.description = getattr(原始工具, "description", "内部函数工具")
        return 用户工具包装器


class 简化内部函数包装器:
    """简化的内部函数包装器 - 基于FastAPI依赖注入"""

    def __init__(self):
        self.已注册工具 = {}  # 工具名称 -> 工具实例
        self.工具元数据 = {}  # 工具名称 -> 元数据
        self.已初始化 = False

    async def 初始化(self):
        """初始化内部函数包装器"""
        try:
            if self.已初始化:
                return

            内部函数包装器日志器.info("🚀 开始初始化简化内部函数包装器...")

            # 注册预定义的业务函数工具
            await self._注册预定义工具()

            # 确保工具在数据库中注册
            await self._确保工具数据库注册()

            self.已初始化 = True
            内部函数包装器日志器.info(
                f"✅ 简化内部函数包装器初始化完成，注册了 {len(self.已注册工具)} 个工具"
            )

        except Exception as e:
            内部函数包装器日志器.error(f"❌ 内部函数包装器初始化失败: {str(e)}")
            raise

    async def _注册预定义工具(self):
        """注册预定义的业务函数工具 - 简化版本"""
        try:
            if not LANGCHAIN_AVAILABLE:
                内部函数包装器日志器.warning("⚠️ LangChain不可用，跳过工具注册")
                return

            # 注册基础工具
            self._注册字符串相加工具()
            self._注册用户信息查询工具()
            self._注册认领达人工具()
            self._注册时间查询工具()

            内部函数包装器日志器.info("✅ 预定义工具注册完成")

        except Exception as e:
            内部函数包装器日志器.error(f"❌ 注册预定义工具失败: {str(e)}")

    def _注册字符串相加工具(self):
        """注册字符串相加工具"""

        @简化内部函数工具("字符串相加", "将两个字符串相加")
        def 字符串相加(字符串1: str, 字符串2: str, 用户id: int = 1) -> str:
            try:
                if not isinstance(用户id, int) or 用户id <= 0:
                    return "错误：无效的用户id"

                结果 = 字符串1 + 字符串2
                内部函数包装器日志器.debug(
                    f"用户 {用户id} 字符串相加: '{字符串1}' + '{字符串2}' = '{结果}'"
                )
                return 结果
            except Exception as e:
                return f"字符串相加失败: {str(e)}"

        self.已注册工具["字符串相加"] = 字符串相加
        self.工具元数据["字符串相加"] = {
            "分类": "基础工具",
            "权限要求": "基础.使用",
            "描述": "将两个字符串相加",
        }

    def _注册用户信息查询工具(self):
        """注册统一的用户信息查询工具"""

        @简化内部函数工具("查询用户信息", "查询用户的完整信息")
        async def 查询用户信息(用户id: int) -> str:
            try:
                # 导入用户服务
                from 数据.用户 import 获取用户信息

                用户数据 = await 获取用户信息(用户id)
                if not 用户数据:
                    return f"用户id {用户id} 不存在"

                # 返回完整的用户信息
                用户信息 = {
                    "用户id": 用户数据.get("id"),
                    "昵称": 用户数据.get("昵称", ""),
                    "手机号": 用户数据.get("手机号", ""),
                    "邮箱": 用户数据.get("邮箱", ""),
                    "状态": 用户数据.get("状态", ""),
                    "等级": 用户数据.get("level", 1),
                    "算力值": 用户数据.get("算力值", 0),
                    "每日邀约次数": 用户数据.get("每日邀约次数", 0),
                    "是否管理员": 用户数据.get("is_admin", False),
                    "创建时间": str(用户数据.get("created_at", "")),
                }

                return json.dumps(用户信息, ensure_ascii=False)

            except Exception as e:
                return f"查询用户信息失败: {str(e)}"

        self.已注册工具["查询用户信息"] = 查询用户信息
        self.工具元数据["查询用户信息"] = {
            "分类": "用户管理",
            "权限要求": "用户.查询",
            "描述": "查询用户的完整信息，包括基本信息、权限信息等",
        }

        # 添加"查询我的信息"工具 - 安全版本，只使用传入的用户ID
        @简化内部函数工具("查询我的信息", "查询当前用户的完整信息（安全隔离）")
        async def 查询我的信息() -> str:
            try:
                # 从线程本地存储获取当前用户ID（这是传入的用户ID）
                当前用户id = 获取当前用户id()
                if not 当前用户id or not isinstance(当前用户id, int) or 当前用户id <= 0:
                    内部函数包装器日志器.error(f"❌ 安全检查失败：无效的用户ID: {当前用户id}")
                    return "安全检查失败：无法获取有效的用户ID"

                内部函数包装器日志器.info(f"🔒 安全查询用户信息：用户ID={当前用户id}")

                # 导入用户服务
                from 数据.用户 import 获取用户信息

                用户数据 = await 获取用户信息(当前用户id)
                if not 用户数据:
                    内部函数包装器日志器.warning(f"⚠️ 用户不存在：ID={当前用户id}")
                    return f"用户(ID: {当前用户id})不存在"

                # 返回完整的用户信息
                用户信息 = {
                    "用户id": 用户数据.get("id"),
                    "昵称": 用户数据.get("昵称", ""),
                    "手机号": 用户数据.get("手机号", ""),
                    "邮箱": 用户数据.get("邮箱", ""),
                    "状态": 用户数据.get("状态", ""),
                    "等级": 用户数据.get("level", 1),
                    "算力值": 用户数据.get("算力值", 0),
                    "每日邀约次数": 用户数据.get("每日邀约次数", 0),
                    "是否管理员": 用户数据.get("is_admin", False),
                    "创建时间": str(用户数据.get("created_at", "")),
                }

                内部函数包装器日志器.info(f"✅ 安全查询成功：用户ID={当前用户id}")
                return json.dumps(用户信息, ensure_ascii=False)

            except Exception as e:
                内部函数包装器日志器.error(f"❌ 查询我的信息失败: {str(e)}")
                return f"查询我的信息失败: {str(e)}"

        self.已注册工具["查询我的信息"] = 查询我的信息
        self.工具元数据["查询我的信息"] = {
            "分类": "用户管理",
            "权限要求": "用户.查询",
            "描述": "查询当前用户的完整信息，自动使用智能体关联的用户ID",
        }

    def _注册认领达人工具(self):
        """注册认领达人工具"""

        @简化内部函数工具("认领达人", "为用户认领指定的达人")
        async def 认领达人(达人id: int, 用户id: int) -> str:
            try:
                # 导入达人服务
                from 服务.达人服务 import 认领达人 as 执行认领达人

                # 执行认领操作
                结果 = await 执行认领达人(用户id, 达人id)

                if 结果:
                    return f"用户id {用户id} 成功认领达人id {达人id}"
                else:
                    return f"用户id {用户id} 认领达人id {达人id} 失败"

            except ValueError as e:
                # 业务逻辑错误
                return f"认领达人失败: {str(e)}"
            except Exception as e:
                return f"认领达人系统错误: {str(e)}"

        self.已注册工具["认领达人"] = 认领达人
        self.工具元数据["认领达人"] = {
            "分类": "达人管理",
            "权限要求": "达人.认领",
            "描述": "为用户认领指定的达人",
        }

    def _注册时间查询工具(self):
        """注册时间查询工具"""

        @简化内部函数工具("获取当前时间", "获取当前系统时间")
        def 获取当前时间() -> str:
            try:
                from datetime import datetime

                return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            except Exception as e:
                内部函数包装器日志器.error(f"❌ 获取当前时间失败: {str(e)}")
                return f"获取时间失败: {str(e)}"

        # 修复：添加工具到已注册工具字典
        self.已注册工具["获取当前时间"] = 获取当前时间
        self.工具元数据["获取当前时间"] = {
            "分类": "内部工具",
            "权限要求": "",
            "描述": "获取当前系统时间",
        }

    async def _确保工具数据库注册(self):
        """确保工具在数据库中注册 - 使用统一工具数据层"""
        try:
            from 数据.LangChain_工具数据层 import LangChain工具数据层实例

            # 初始化工具数据层
            if not LangChain工具数据层实例.已初始化:
                await LangChain工具数据层实例.初始化()

            for 工具名称, 元数据 in self.工具元数据.items():
                工具配置 = {
                    "工具名称": 工具名称,
                    "工具描述": 元数据["描述"],
                    "工具参数": "",  # 内部函数工具参数通过装饰器定义
                    "权限要求": 元数据["权限要求"]
                    if isinstance(元数据["权限要求"], str)
                    else "",
                    "安全级别": 1,
                    "启用状态": True,
                }
                await LangChain工具数据层实例.创建工具配置(工具配置)

            内部函数包装器日志器.info("✅ 工具数据库注册完成")

        except Exception as e:
            内部函数包装器日志器.error(f"❌ 工具数据库注册失败: {str(e)}")

    def 获取用户工具列表(self, 用户id: int) -> Dict[str, Any]:
        """为指定用户获取可用的工具列表"""
        if not self.已初始化:
            内部函数包装器日志器.warning("⚠️ 包装器未初始化，返回空工具列表")
            return {}

        用户工具 = {}
        for 工具名称, 原始工具 in self.已注册工具.items():
            # 使用简化的用户工具创建函数
            用户工具[工具名称] = 创建用户工具(原始工具, 用户id)

        内部函数包装器日志器.info(f"✅ 为用户 {用户id} 创建了 {len(用户工具)} 个工具")
        return 用户工具

    async def 获取可用工具列表(self) -> Dict[str, Any]:
        """获取可用工具列表"""
        if not self.已初始化:
            内部函数包装器日志器.warning("⚠️ 包装器未初始化，返回空工具列表")
            return {}
        return self.已注册工具.copy()

    async def 获取工具元数据(self) -> Dict[str, Any]:
        """获取工具元数据"""
        if not self.已初始化:
            内部函数包装器日志器.warning("⚠️ 包装器未初始化，返回空元数据")
            return {}
        return self.工具元数据.copy()


# 创建全局实例
内部函数包装器实例 = 简化内部函数包装器()
