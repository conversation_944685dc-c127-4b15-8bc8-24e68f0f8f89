<template>
  <div class="model-config">
    <div class="section-header">
      <h2><RobotOutlined /> 模型配置</h2>
      <p>选择和配置对话模型参数</p>
    </div>
    
    <a-form layout="vertical" class="config-form">
      <a-card title="模型设置" class="config-card">
        <!-- 对话模型选择 -->
        <a-form-item 
          label="对话模型" 
          required
          :validate-status="errors.langchain_模型配置表id ? 'error' : ''"
          :help="errors.langchain_模型配置表id"
        >
          <a-select
            v-model:value="localForm.langchain_模型配置表id"
            placeholder="选择对话模型"
            :loading="loading.模型列表"
            :options="modelOptions"
            show-search
            :filter-option="filterModelOption"
            @change="handleModelChange"
          >
            <template #option="{ label, value, ...model }">
              <div class="model-option">
                <div class="model-name">{{ label }}</div>
                <div class="model-info">
                  <a-space size="small">
                    <a-tag size="small" color="blue">{{ model.模型类型 }}</a-tag>
                    <a-tag size="small" color="green">{{ model.提供商 }}</a-tag>
                    <span class="model-desc">{{ model.模型描述 }}</span>
                  </a-space>
                </div>
              </div>
            </template>
          </a-select>
        </a-form-item>

        <!-- 选中模型信息 -->
        <div v-if="selectedModelInfo" class="selected-model-info">
          <a-descriptions size="small" :column="2" bordered>
            <a-descriptions-item label="模型名称">
              {{ selectedModelInfo.模型名称 }}
            </a-descriptions-item>
            <a-descriptions-item label="提供商">
              {{ selectedModelInfo.提供商 }}
            </a-descriptions-item>
            <a-descriptions-item label="模型类型">
              {{ selectedModelInfo.模型类型 }}
            </a-descriptions-item>
            <a-descriptions-item label="最大Token">
              {{ selectedModelInfo.最大令牌数 || '未知' }}
            </a-descriptions-item>
            <a-descriptions-item label="描述" :span="2">
              {{ selectedModelInfo.模型描述 || '暂无描述' }}
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 温度参数 -->
        <a-form-item label="温度参数">
          <div class="temperature-control">
            <a-slider
              v-model:value="localForm.温度参数"
              :min="0"
              :max="2"
              :step="0.1"
              :marks="temperatureMarks"
              @change="handleFormChange"
            />
            <div class="temperature-info">
              <a-input-number
                v-model:value="localForm.温度参数"
                :min="0"
                :max="2"
                :step="0.1"
                :precision="1"
                size="small"
                style="width: 80px"
                @change="handleFormChange"
              />
              <span class="temperature-desc">{{ getTemperatureDesc(localForm.温度参数) }}</span>
            </div>
          </div>
        </a-form-item>

        <!-- 最大Token数 -->
        <a-form-item label="最大Token数">
          <a-input-number
            v-model:value="localForm.最大令牌数"
            :min="100"
            :max="selectedModelInfo?.最大令牌数 || 4000"
            :step="100"
            placeholder="输出的最大Token数"
            style="width: 200px"
            @change="handleFormChange"
          />
          <span class="token-hint">
            建议值：{{ Math.floor((selectedModelInfo?.最大令牌数 || 4000) * 0.7) }}
          </span>
        </a-form-item>

        <!-- 高级参数 -->
        <a-collapse ghost>
          <a-collapse-panel key="advanced" header="高级参数">
            <a-form-item label="Top P">
              <a-slider
                v-model:value="localForm.top_p"
                :min="0"
                :max="1"
                :step="0.1"
                :marks="{ 0: '0', 0.5: '0.5', 1: '1' }"
                @change="handleFormChange"
              />
              <div class="param-desc">
                控制输出的多样性，值越小输出越确定
              </div>
            </a-form-item>

            <a-form-item label="Frequency Penalty">
              <a-slider
                v-model:value="localForm.frequency_penalty"
                :min="0"
                :max="2"
                :step="0.1"
                :marks="{ 0: '0', 1: '1', 2: '2' }"
                @change="handleFormChange"
              />
              <div class="param-desc">
                降低重复内容的概率
              </div>
            </a-form-item>

            <a-form-item label="Presence Penalty">
              <a-slider
                v-model:value="localForm.presence_penalty"
                :min="0"
                :max="2"
                :step="0.1"
                :marks="{ 0: '0', 1: '1', 2: '2' }"
                @change="handleFormChange"
              />
              <div class="param-desc">
                鼓励谈论新话题
              </div>
            </a-form-item>
          </a-collapse-panel>
        </a-collapse>
      </a-card>
    </a-form>
  </div>
</template>

<script setup>
import { computed, watch } from 'vue'
import { RobotOutlined } from '@ant-design/icons-vue'

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  },
  errors: {
    type: Object,
    default: () => ({})
  },
  modelOptions: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'validate', 'change'])

// 本地表单数据
const localForm = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 温度参数标记
const temperatureMarks = {
  0: '0',
  0.3: '0.3',
  0.7: '0.7',
  1: '1',
  1.5: '1.5',
  2: '2'
}

// 选中的模型信息
const selectedModelInfo = computed(() => {
  if (!localForm.value.langchain_模型配置表id) return null
  return props.modelOptions.find(model => model.value === localForm.value.langchain_模型配置表id)
})

// 获取温度描述
const getTemperatureDesc = (temperature) => {
  if (temperature <= 0.3) return '保守 - 输出更确定'
  if (temperature <= 0.7) return '平衡 - 推荐设置'
  if (temperature <= 1.2) return '创意 - 输出更多样'
  return '随机 - 高度创意'
}

// 过滤模型选项
const filterModelOption = (input, option) => {
  const searchText = input.toLowerCase()
  return option.label.toLowerCase().includes(searchText) ||
         option.模型类型?.toLowerCase().includes(searchText) ||
         option.提供商?.toLowerCase().includes(searchText)
}

// 处理模型变化
const handleModelChange = (modelId) => {
  const modelInfo = props.modelOptions.find(model => model.value === modelId)
  if (modelInfo) {
    // 自动设置推荐的最大Token数
    if (!localForm.value.最大令牌数 && modelInfo.最大令牌数) {
      localForm.value.最大令牌数 = Math.floor(modelInfo.最大令牌数 * 0.7)
    }
  }
  handleFormChange()
}

// 处理表单变化
const handleFormChange = () => {
  emit('change', localForm.value)
}

// 监听模型id变化，清除验证错误
watch(() => localForm.value.langchain_模型配置表id, () => {
  if (props.errors.langchain_模型配置表id) {
    emit('validate', { langchain_模型配置表id: null })
  }
})

// 初始化高级参数默认值
if (!localForm.value.top_p) localForm.value.top_p = 1
if (!localForm.value.frequency_penalty) localForm.value.frequency_penalty = 0
if (!localForm.value.presence_penalty) localForm.value.presence_penalty = 0
if (!localForm.value.最大令牌数) localForm.value.最大令牌数 = 2000
</script>

<style scoped>
.model-config {
  padding: 16px;
}

.section-header {
  margin-bottom: 24px;
}

.section-header h2 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-header p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.config-form {
  max-width: 600px;
}

.config-card {
  margin-bottom: 16px;
}

.model-option {
  padding: 4px 0;
}

.model-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.model-info {
  font-size: 12px;
}

.model-desc {
  color: #8c8c8c;
}

.selected-model-info {
  margin: 16px 0;
  padding: 16px;
  background: #f6f8fa;
  border-radius: 6px;
}

.temperature-control {
  width: 100%;
}

.temperature-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 8px;
}

.temperature-desc {
  color: #8c8c8c;
  font-size: 12px;
}

.token-hint {
  margin-left: 12px;
  color: #8c8c8c;
  font-size: 12px;
}

.param-desc {
  margin-top: 4px;
  color: #8c8c8c;
  font-size: 12px;
}

:deep(.ant-collapse-ghost .ant-collapse-item) {
  border-bottom: none;
}

:deep(.ant-collapse-ghost .ant-collapse-content) {
  background: transparent;
}
</style>
