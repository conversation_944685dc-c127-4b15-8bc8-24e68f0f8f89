"""
LangChain智能体常量定义 - 统一管理所有硬编码的常量值
"""

from enum import Enum

# 智能体状态常量
class 智能体状态(Enum):
    """智能体运行状态枚举"""
    未初始化 = "未初始化"
    初始化中 = "初始化中"
    运行中 = "运行中"
    错误 = "错误"
    已停止 = "已停止"

# 输出格式常量
class 输出格式(Enum):
    """智能体输出格式枚举"""
    TEXT = "text"
    JSON = "json"
    STRUCTURED = "structured"

# 链类型常量
class 链类型(Enum):
    """LangChain链类型枚举"""
    基础对话链 = "basic_chat"
    RAG链 = "rag_chain"
    工具链 = "tool_chain"
    复合链 = "composite_chain"

# 检索策略常量
class 检索策略(Enum):
    """RAG检索策略枚举"""
    SIMILARITY = "similarity"
    MMR = "mmr"
    SIMILARITY_SCORE_THRESHOLD = "similarity_score_threshold"

# 默认配置常量
class 默认配置:
    """默认配置值"""
    
    # RAG检索配置
    最大检索数量 = 5
    相似度阈值 = 0.7
    默认检索策略 = 检索策略.SIMILARITY.value
    
    # 模型配置
    默认温度 = 0.7
    最大令牌数 = 4000
    
    # 分页配置
    默认页码 = 1
    默认每页数量 = 10
    最大每页数量 = 100
    
    # 缓存配置
    提示词缓存大小 = 128
    工具缓存大小 = 64
    
    # 超时配置
    对话超时秒数 = 30
    检索超时秒数 = 10
    工具调用超时秒数 = 15

# 错误类型常量
class 错误类型(Enum):
    """错误类型枚举"""
    RAG_RETRIEVAL_ERROR = "RAG检索错误"
    JSON_FORMAT_ERROR = "JSON格式化错误"
    TOOL_CALL_ERROR = "工具调用错误"
    CHAIN_BUILD_ERROR = "链构建错误"
    GENERAL_ERROR = "通用错误"
    ERROR_HANDLER_FAILURE = "错误处理器异常"

# JSON Schema常量
class JSON模式常量:
    """JSON Schema相关常量"""
    
    # 基础字段类型映射
    字段类型映射 = {
        "string": str,
        "integer": int,
        "number": float,
        "boolean": bool,
        "array": list,
        "object": dict
    }
    
    # 默认JSON示例
    默认JSON示例 = '{"后续建议":"示例后续建议内容","回复内容":[{"内容":"示例回复内容","类型":"文本"}]}'
    
    # JSON输出指令模板
    JSON输出指令模板 = """
请严格按照JSON格式输出您的回复。输出必须是有效的JSON格式，不要包含任何额外的文本或解释。

⚠️ 重要：您的回复必须使用JSON格式，这是强制性要求。

基本格式要求：
- 使用双引号包围字符串
- 确保JSON语法正确
- 不要在JSON前后添加额外文本
- 必须输出标准的JSON对象格式

🎯 特别注意回复内容字段格式：
- "回复内容"字段必须是对象数组，不是字符串数组
- 每个数组元素都必须是包含"内容"和"类型"字段的对象
- 格式：[{{"内容": "具体消息内容", "类型": "文本"}}]
- 禁止使用：["文本消息1", "文本消息2"] 这种字符串数组格式

请确保您的所有回复都是有效的JSON格式。
"""

# 提示词模板常量
class 提示词模板常量:
    """提示词模板相关常量"""
    
    # 系统提示词组件标识符
    角色身份标识 = "🎭 角色身份："
    系统指导标识 = "🤖 系统指导："
    行为规范标识 = "📋 行为规范："
    输出格式标识 = "📄 输出格式要求："
    
    # 变量占位符
    知识库上下文占位符 = "{knowledge_context}"
    用户问题占位符 = "{user_question}"
    聊天记录占位符 = "{chat_history}"
    
    # 标准变量字典
    标准变量字典 = {
        "input": "",
        "knowledge_context": "",
        "user_question": "",
        "chat_history": "",
        "聊天记录": "",
        "current_time": "",
        "current_date": "",
        "agent_name": "",
        "性格": "",
        "爱好": "",
        "后续建议": "",
        "内容": "",
        "类型": "",
    }

# 工具相关常量
class 工具常量:
    """工具相关常量"""
    
    # 工具状态
    工具状态_启用 = "enabled"
    工具状态_禁用 = "disabled"
    
    # 工具类型
    工具类型_数据库 = "database"
    工具类型_内部函数 = "internal_function"
    工具类型_外部API = "external_api"
    
    # 权限级别
    权限级别_公开 = "public"
    权限级别_私有 = "private"
    权限级别_管理员 = "admin"

# 数据库相关常量
class 数据库常量:
    """数据库相关常量"""
    
    # 表名
    智能体配置表 = "langchain_智能体配置表"
    提示词配置表 = "langchain_提示词配置表"
    模型配置表 = "langchain_模型配置表"
    知识库表 = "langchain_知识库表"
    智能体知识库关联表 = "langchain_智能体知识库关联表"
    
    # 启用状态值
    启用状态_启用 = True
    启用状态_禁用 = False
    
    # 字段名
    字段_智能体id = "智能体id"
    字段_用户id = "用户表id"
    字段_启用rag = "启用rag"
    字段_输出格式 = "输出格式"
    字段_是否启用 = "是否启用"

# 日志相关常量
class 日志常量:
    """日志相关常量"""
    
    # 日志级别
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"
    
    # 日志格式
    标准日志格式 = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    详细日志格式 = "%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s"
    
    # 日志文件
    智能体日志文件 = "logs/agent.log"
    错误日志文件 = "logs/error.log"
    性能日志文件 = "logs/performance.log"

# 性能监控常量
class 性能常量:
    """性能监控相关常量"""
    
    # 性能阈值（秒）
    对话响应阈值 = 5.0
    RAG检索阈值 = 3.0
    工具调用阈值 = 2.0
    
    # 监控指标
    响应时间指标 = "response_time"
    令牌消耗指标 = "token_usage"
    错误率指标 = "error_rate"
    
    # 统计周期
    统计周期_分钟 = 60
    统计周期_小时 = 3600
    统计周期_天 = 86400

# API响应常量
class API响应常量:
    """API响应相关常量"""
    
    # 状态码
    成功状态码 = 100
    错误状态码 = 500
    参数错误状态码 = 400
    权限错误状态码 = 403
    
    # 响应消息
    成功消息 = "操作成功"
    错误消息 = "操作失败"
    参数错误消息 = "参数错误"
    权限错误消息 = "权限不足"
    
    # 响应格式
    标准响应格式 = {
        "status": 成功状态码,
        "message": 成功消息,
        "data": None,
        "timestamp": None
    }
