"""
微信达人管理API路由

功能概述：
- 提供微信达人管理的所有API接口
- 支持达人公海、认领、详情查看等功能
- 提供统一的响应格式和错误处理
- 优化后的统一微信达人管理接口

作者: CRM系统开发团队
创建时间: 2024-06-25
更新时间: 2024-12-19
"""

from typing import Any, Dict, Optional

from fastapi import APIRouter, Body, Depends
from pydantic import BaseModel

# 导入认证依赖
from 依赖项.认证 import 获取当前用户
from 数据模型.响应模型 import 统一响应模型
from 数据模型.微信达人模型 import 微信达人查询请求模型, 微信达人认领请求模型

# 导入统一日志系统
from 日志 import 接口日志器, 错误日志器

# 导入微信达人服务
from 服务.微信达人服务 import (
    取消认领微信达人,
    搜索用户认领微信达人列表,
    查询或更新微信达人,
    获取微信达人公海列表,
    获取微信达人详情,
    认领微信达人,
)

# 创建微信达人路由器
微信小店达人路由 = APIRouter(
    tags=["微信达人管理"],
    responses={404: {"description": "接口未找到"}},
)


# Pydantic模型定义
class 获取微信达人统计请求(BaseModel):
    时间范围: Optional[int] = 7  # 默认7天，用于统计新增达人


class 获取微信达人公海请求(BaseModel):
    页码: int = 1
    每页数量: int = 20  # 优化为每次加载20个达人
    最后ID: Optional[int] = 0
    筛选条件: Optional[Dict[str, Any]] = None
    有联系方式: Optional[bool] = None
    关键词: Optional[str] = None


class 获取微信达人详情请求(BaseModel):
    达人id: int


class 查询或更新微信达人请求(BaseModel):
    微信号: str
    达人数据: Optional[Dict[str, Any]] = None


@微信小店达人路由.post(
    "/list",
    summary="获取微信达人公海列表",
    description="获取微信达人公海列表，支持流式分页和多种筛选条件",
)
async def 微信达人公海列表(
    请求: 获取微信达人公海请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    获取微信达人公海列表

    支持功能：
    - 流式分页（通过最后ID实现无限滚动）
    - 关键词搜索（微信号、昵称）
    - 联系方式筛选
    - 多种筛选条件（地区、性别等）

    参数:
        页码: 当前页码，默认为1
        每页数量: 每页显示数量，默认为20
        最后ID: 上一页最后的达人id，用于流式分页
        筛选条件: 可选的筛选条件
        有联系方式: 是否筛选有联系方式的达人
        关键词: 搜索关键词

    返回:
        包含微信达人列表和分页信息的统一响应
    """
    try:
        # 从认证中获取用户信息
        用户id = 当前用户["id"]
        团队id = 当前用户.get("team_id")  # 如果有团队信息

        # 如果用户信息中没有团队id，尝试获取用户的默认团队
        if not 团队id:
            try:
                from 数据.团队成员数据 import 获取用户默认团队id

                团队id = await 获取用户默认团队id(用户id)
                接口日志器.info(
                    f"用户 {用户id} 未在认证信息中包含团队id，已获取默认团队id: {团队id}"
                )
            except Exception as e:
                接口日志器.warning(
                    f"获取用户 {用户id} 默认团队id失败: {e}，将使用兼容模式"
                )
                团队id = None

        接口日志器.info(f"用户 {用户id} 请求微信达人公海列表，团队id: {团队id}")

        # 调用服务层获取达人列表
        达人列表 = await 获取微信达人公海列表(
            页码=请求.页码,
            每页数量=请求.每页数量,
            最后ID=请求.最后ID,
            筛选条件=请求.筛选条件,
            有联系方式=请求.有联系方式,
            关键词=请求.关键词,
            当前用户id=用户id,
            当前团队id=团队id,
        )

        # 返回成功响应
        return 统一响应模型.成功(达人列表, "获取微信达人公海列表成功")

    except Exception as e:
        错误日志器.error(f"微信达人公海列表接口异常: {str(e)}")
        # 返回失败响应
        return 统一响应模型.失败(500, f"获取微信达人公海列表失败: {str(e)}")


@微信小店达人路由.post(
    "/detail",
    summary="获取微信达人详情",
    description="获取微信达人详情信息，包含联系方式和认领状态",
)
async def 微信达人详情(
    请求: 获取微信达人详情请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    获取微信达人详情，包含联系方式信息和当前用户认领状态

    参数:
        达人id: 达人的唯一标识ID

    返回:
        包含达人详情、联系方式和认领状态的统一响应
    """
    try:
        # 从认证中获取用户id
        用户id = 当前用户["id"]

        接口日志器.info(f"用户 {用户id} 请求微信达人 {请求.达人id} 详情")

        # 获取达人详情
        达人信息 = await 获取微信达人详情(请求.达人id, 用户id)

        # 返回成功响应
        return 统一响应模型.成功(达人信息, "获取微信达人详情成功")

    except ValueError as e:
        # 业务逻辑错误
        接口日志器.warning(f"微信达人详情请求失败 - 业务错误: {str(e)}")
        return 统一响应模型.失败(400, str(e))
    except Exception as e:
        错误日志器.error(f"微信达人详情接口异常: {str(e)}")
        # 返回失败响应
        return 统一响应模型.失败(500, f"获取微信达人详情失败: {str(e)}")


@微信小店达人路由.post(
    "/claim", summary="认领微信达人", description="将指定的微信达人认领到当前用户名下"
)
async def 认领微信达人接口(
    请求: 微信达人认领请求模型 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    认领微信达人

    将指定的微信达人认领到当前用户名下，认领后该达人会出现在"我的达人"列表中

    参数:
        达人id: 要认领的达人id

    返回:
        认领操作结果的统一响应
    """
    try:
        # 从认证中获取用户信息
        用户id = 当前用户["id"]
        团队id = 当前用户.get("team_id")

        接口日志器.info(f"用户 {用户id} 请求认领微信达人 {请求.达人id}")

        # 执行认领操作
        结果 = await 认领微信达人(请求.达人id, 用户id, 团队id)

        # 根据结果状态返回响应
        if 结果["状态"] == "成功":
            return 统一响应模型.成功({"达人id": 结果["达人id"]}, 结果["消息"])
        else:
            return 统一响应模型.失败(400, 结果["消息"])

    except Exception as e:
        错误日志器.error(f"认领微信达人接口异常: {str(e)}")
        # 返回失败响应
        return 统一响应模型.失败(500, f"认领微信达人失败: {str(e)}")


@微信小店达人路由.post(
    "/unclaim", summary="取消认领微信达人", description="取消对指定微信达人的认领关系"
)
async def 取消认领微信达人接口(
    请求: 微信达人认领请求模型 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    取消认领微信达人

    取消对指定微信达人的认领关系，取消后该达人重新回到公海中

    参数:
        达人id: 要取消认领的达人id

    返回:
        取消认领操作结果的统一响应
    """
    try:
        # 从认证中获取用户id
        用户id = 当前用户["id"]

        接口日志器.info(f"用户 {用户id} 请求取消认领微信达人 {请求.达人id}")

        # 执行取消认领操作
        结果 = await 取消认领微信达人(请求.达人id, 用户id)

        # 根据结果状态返回响应
        if 结果["状态"] == "成功":
            return 统一响应模型.成功({"达人id": 结果["达人id"]}, 结果["消息"])
        else:
            return 统一响应模型.失败(400, 结果["消息"])

    except Exception as e:
        错误日志器.error(f"取消认领微信达人接口异常: {str(e)}")
        # 返回失败响应
        return 统一响应模型.失败(500, f"取消认领微信达人失败: {str(e)}")


@微信小店达人路由.post(
    "/myclaims",
    summary="获取我认领的微信达人列表",
    description="获取当前用户认领的微信达人列表，支持搜索和排序",
)
async def 我的微信达人列表(
    请求: 微信达人查询请求模型 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    获取我认领的微信达人列表

    支持功能：
    - 传统分页
    - 关键词搜索
    - 多种排序方式
    - 筛选条件

    参数:
        页码: 当前页码
        每页数量: 每页显示数量
        排序字段: 排序字段
        排序方式: 排序方式
        筛选条件: 筛选条件
        关键词: 搜索关键词

    返回:
        包含我的微信达人列表和分页信息的统一响应
    """
    try:
        # 从认证中获取用户id
        用户id = 当前用户["id"]

        接口日志器.info(f"用户 {用户id} 请求我的微信达人列表")

        # 调用服务层获取用户认领的达人列表
        达人列表 = await 搜索用户认领微信达人列表(
            用户id=用户id,
            页码=请求.页码,
            每页数量=请求.每页数量,
            排序字段=请求.排序字段,
            排序方式=请求.排序方式,
            筛选条件=请求.筛选条件,
            关键词=请求.关键词,
        )

        # 返回成功响应
        return 统一响应模型.成功(达人列表, "获取我的微信达人列表成功")

    except Exception as e:
        错误日志器.error(f"我的微信达人列表接口异常: {str(e)}")
        # 返回失败响应
        return 统一响应模型.失败(500, f"获取我的微信达人列表失败: {str(e)}")


@微信小店达人路由.post(
    "/get_id",
    summary="查询或创建微信达人",
    description="通过微信号查询或创建微信达人，返回达人id",
)
async def 查询或更新微信达人接口(
    请求: 查询或更新微信达人请求 = Body(...), _当前用户: dict = Depends(获取当前用户)
):
    """
    通过微信号查询或创建微信达人，并返回其系统内ID

    参数:
        微信号: 微信号（必需）
        达人数据: 可选，达人数据用于创建或更新

    返回:
        包含达人id的统一响应
    """
    try:
        接口日志器.info(f"查询或更新微信达人 - 微信号: {请求.微信号}")

        # 执行查询或更新操作
        结果 = await 查询或更新微信达人(请求.微信号, 请求.达人数据)

        # 根据结果状态返回响应
        if 结果["状态"] == "成功":
            # 只返回达人id，而不是整个结果对象
            return 统一响应模型.成功({"达人id": 结果["达人id"]}, 结果["消息"])
        else:
            # 即便操作失败，也可能包含有用的消息
            return 统一响应模型.失败(
                400, 结果.get("消息", "查询或创建微信达人id时发生未知错误")
            )

    except Exception as e:
        错误日志器.error(f"查询或更新微信达人接口异常: {str(e)}")
        # 返回失败响应
        return 统一响应模型.失败(500, f"查询或更新微信达人接口失败: {str(e)}")


@微信小店达人路由.post(
    "/stats", summary="获取微信达人统计", description="获取用户微信达人统计数据"
)
async def 获取微信达人统计数据接口(
    请求: 获取微信达人统计请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    获取微信达人统计数据

    获取用户认领的微信达人统计信息，包括总数、有联系方式数量、新增数量等。

    参数:
        时间范围: 统计时间范围（天数），默认7天

    返回:
        包含微信达人统计数据的统一响应
    """
    try:
        # 获取用户id
        用户id = 当前用户["id"]

        接口日志器.info(f"微信达人统计接口已移除，用户id: {用户id}")

        # 返回简化的统计数据
        简化统计 = {
            "总数": 0,
            "有联系方式": 0,
        }

        return 统一响应模型.成功(简化统计, "微信达人统计接口已简化")

    except Exception as e:
        错误日志器.error(f"获取微信达人统计数据失败: {str(e)}")
        return 统一响应模型.失败(500, f"获取微信达人统计数据失败: {str(e)}")
