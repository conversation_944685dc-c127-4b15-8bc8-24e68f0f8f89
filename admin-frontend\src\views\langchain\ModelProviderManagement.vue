<template>
  <div class="model-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>AI模型管理</h2>
      <p class="page-description">
        查看和管理系统中可用的AI模型，基于现有LangChain模型管理器
      </p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards" v-if="managerStatus">
      <div class="stats-card">
        <div class="stats-number">{{ totalModels }}</div>
        <div class="stats-label">总模型数</div>
      </div>
      <div class="stats-card">
        <div class="stats-number">{{ availableModels }}</div>
        <div class="stats-label">可用模型</div>
      </div>
      <div class="stats-card">
        <div class="stats-number">{{ managerStatus.默认模型 || '未设置' }}</div>
        <div class="stats-label">默认模型</div>
      </div>
      <div class="stats-card">
        <div class="stats-number">{{ managerStatus.已初始化 ? '已初始化' : '未初始化' }}</div>
        <div class="stats-label">管理器状态</div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <div class="filter-row">
        <a-input
          v-model:value="searchKeyword"
          placeholder="搜索模型名称..."
          style="width: 300px"
          @pressEnter="handleSearch"
        >
          <template #prefix>
            <search-outlined />
          </template>
        </a-input>
        
        <a-select
          v-model:value="selectedType"
          placeholder="选择类型"
          style="width: 200px"
          allow-clear
          @change="handleTypeChange"
        >
          <a-select-option
            v-for="type in modelTypes"
            :key="type"
            :value="type"
          >
            {{ type }}
          </a-select-option>
        </a-select>

        <a-button type="primary" @click="handleSearch">
          <search-outlined />
          搜索
        </a-button>

        <a-button @click="handleReset">
          <reload-outlined />
          重置
        </a-button>

        <a-button @click="refreshStatus">
          <sync-outlined />
          刷新状态
        </a-button>

        <a-button @click="refreshModelConfig">
          <reload-outlined />
          刷新配置
        </a-button>

        <a-button @click="showConfigModal">
          <setting-outlined />
          配置管理
        </a-button>

        <a-button type="primary" @click="showCreateModal">
          <plus-outlined />
          新增模型
        </a-button>

        <a-button @click="showEmbeddingModelsList">
          <experiment-outlined />
          向量模型
        </a-button>
      </div>
    </div>

    <!-- 模型列表表格 -->
    <div class="table-section">
      <a-table
        :columns="tableColumns"
        :data-source="modelList"
        :loading="loading"
        :pagination="paginationConfig"
        @change="handleTableChange"
        row-key="id"
        size="middle"
      >
        <!-- 供应商列 -->
        <template #bodyCell="{ column, record }">
          <!-- 供应商列 -->
          <template v-if="column.key === '提供商'">
            <a-tag :color="getTypeColor(record.提供商)">
              {{ record.提供商 }}
            </a-tag>
          </template>

          <!-- 模型类型列 -->
          <template v-else-if="column.key === '模型类型'">
            <a-tag :color="getModelTypeColor(record.模型类型)">
              {{ record.模型类型 }}
            </a-tag>
          </template>

          <!-- 状态列 -->
          <template v-else-if="column.key === '状态'">
            <a-tag :color="record.状态 === '已加载' ? 'green' : 'orange'">
              {{ record.状态 }}
            </a-tag>
          </template>

          <!-- 最大令牌列 -->
          <template v-else-if="column.key === '最大令牌数'">
            <span>{{ record.最大令牌数 || record.最大令牌 || 'N/A' }}</span>
          </template>

          <!-- 算力消耗列 -->
          <template v-else-if="column.key === '算力消耗'">
            <a-tag color="blue">{{ record.算力消耗 || 1 }}</a-tag>
          </template>

          <!-- 操作列 -->
          <template v-else-if="column.key === 'actions'">
            <a-space>
              <a-button type="link" size="small" @click="viewModel(record)">
                <eye-outlined />
                查看
              </a-button>
              
              <!-- 根据模型类型显示不同的测试按钮 -->
              <a-button
                v-if="record.模型类型 && (record.模型类型.toLowerCase().includes('embedding') || record.模型类型.includes('向量'))"
                type="link"
                size="small"
                @click="showEmbeddingTestModal(record)"
              >
                <experiment-outlined />
                向量测试
              </a-button>
              <a-button
                v-else
                type="link"
                size="small"
                @click="testModel(record)"
              >
                <sync-outlined />
                对话测试
              </a-button>
              
              <a-button type="link" size="small" @click="editModel(record)">
                <edit-outlined />
                编辑
              </a-button>
              <a-button type="link" size="small" @click="setDefaultModel(record)">
                <star-outlined />
                设为默认
              </a-button>
              <a-button type="link" size="small" danger @click="deleteModel(record)">
                <delete-outlined />
                删除
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 创建/编辑模型配置模态框 -->
    <a-modal
      v-model:open="createModalVisible"
      :title="isEditing ? '编辑模型配置' : '新增模型配置'"
      width="800"
      @ok="handleCreateModel"
      @cancel="closeCreateModal"
      :confirm-loading="createLoading"
    >
      <a-form
        ref="createFormRef"
        :model="createForm"
        :rules="createFormRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="供应商名称" name="供应商名称" :rules="createFormRules.供应商名称">
              <a-select 
                v-model:value="createForm.供应商名称" 
                placeholder="请选择供应商"
                @change="handleProviderChange"
              >
                <a-select-option v-for="option in providerOptions" :key="option.value" :value="option.value">
                  {{ option.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="模型功能类型" name="模型功能类型" :rules="createFormRules.模型功能类型">
              <a-select 
                v-model:value="createForm.模型功能类型" 
                placeholder="请选择模型功能类型"
                @change="handleFunctionTypeChange"
              >
                <a-select-option v-for="option in modelFunctionTypes" :key="option.value" :value="option.value">
                  <div>
                    <div>{{ option.label }}</div>
                    <div style="font-size: 12px; color: #8c8c8c;">{{ option.description }}</div>
                  </div>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="模型名称" name="模型名称">
              <a-input
                v-model:value="createForm.模型名称"
                placeholder="输入模型名称"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="是否启用" name="是否启用">
              <a-switch v-model:checked="createForm.是否启用" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="API密钥" name="API密钥">
          <a-input-password
            v-model:value="createForm.API密钥"
            placeholder="输入API密钥"
          />
        </a-form-item>

        <a-form-item label="API基础URL" name="API基础URL">
          <a-input
            v-model:value="createForm.API基础URL"
            placeholder="输入API基础URL（可选）"
          />
        </a-form-item>

        <a-form-item label="模型参数">
          <a-textarea
            v-model:value="modelParamsText"
            placeholder="输入JSON格式的模型参数（可选）"
            :rows="4"
            @blur="parseModelParams"
          />
          <div class="form-help">
            示例: {"temperature": 0.7, "max_tokens": 4000}
          </div>
        </a-form-item>

        <a-form-item label="备注">
          <a-textarea
            v-model:value="createForm.备注"
            placeholder="输入备注信息（可选）"
            :rows="2"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 模型详情抽屉 -->
    <a-drawer
      v-model:open="drawerVisible"
      title="模型详情"
      width="600"
      @close="closeDrawer"
    >
      <div v-if="selectedModel" class="model-detail">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h3>基本信息</h3>
          <a-descriptions :column="1" bordered>
            <a-descriptions-item label="模型名称">
              {{ selectedModel.名称 }}
            </a-descriptions-item>
            <a-descriptions-item label="模型标识">
              {{ selectedModel.模型名 || selectedModel.模型标识 }}
            </a-descriptions-item>
            <a-descriptions-item label="供应商">
              <a-tag :color="getTypeColor(selectedModel.类型)">
                {{ selectedModel.类型 }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="状态">
              <a-tag :color="selectedModel.状态 === '已加载' ? 'green' : 'orange'">
                {{ selectedModel.状态 }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="最大令牌数">
              {{ selectedModel.最大令牌数 || selectedModel.最大令牌 || 'N/A' }}
            </a-descriptions-item>
            <a-descriptions-item label="算力消耗">
              {{ selectedModel.算力消耗 || 1 }}
            </a-descriptions-item>
            <a-descriptions-item label="数据库ID">
              {{ selectedModel.数据库ID }}
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 技术信息 -->
        <div class="detail-section">
          <h3>技术信息</h3>
          <a-descriptions :column="1" bordered>
            <a-descriptions-item label="模型类型">
              {{ selectedModel.模型类型 || 'chat' }}
            </a-descriptions-item>
            <a-descriptions-item label="API基础URL" v-if="selectedModel.API基础URL">
              {{ selectedModel.API基础URL }}
            </a-descriptions-item>
            <a-descriptions-item label="描述" v-if="selectedModel.描述">
              {{ selectedModel.描述 }}
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 默认参数 -->
        <div class="detail-section" v-if="selectedModel.默认参数 && Object.keys(selectedModel.默认参数).length > 0">
          <h3>默认参数</h3>
          <a-card size="small">
            <pre>{{ JSON.stringify(selectedModel.默认参数, null, 2) }}</pre>
          </a-card>
        </div>
      </div>
    </a-drawer>

    <!-- 模型测试对话框 -->
    <a-modal
      v-model:open="testModalVisible"
      title="测试模型连接"
      width="600"
      @ok="handleTestModel"
      @cancel="closeTestModal"
      :confirm-loading="testLoading"
    >
      <div v-if="testingModel" class="test-modal">
        <a-form layout="vertical">
          <a-form-item label="测试模型">
            <a-input :value="testingModel.名称" disabled />
          </a-form-item>
          <a-form-item label="测试消息">
            <a-textarea
              v-model:value="testMessage"
              placeholder="输入测试消息..."
              :rows="3"
              :maxlength="100"
              show-count
            />
          </a-form-item>
        </a-form>

        <!-- 测试过程状态 -->
        <div v-if="testLoading" class="test-progress">
          <a-divider>测试进行中</a-divider>
          <a-spin size="large">
            <div style="text-align: center; padding: 20px;">
              <p>正在执行模型测试...</p>
              <p style="color: #666; font-size: 12px;">
                这可能需要几秒钟时间，请耐心等待
              </p>
            </div>
          </a-spin>
        </div>

        <!-- 测试错误显示 -->
        <div v-if="testError" class="test-error">
          <a-divider>测试错误</a-divider>
          <a-alert
            type="error"
            :message="testError"
            show-icon
            style="margin-bottom: 16px;"
          />
        </div>

        <!-- 测试结果 -->
        <div v-if="testResult" class="test-result">
          <h4>测试结果</h4>
          <a-alert
            :type="testResult.success ? 'success' : 'error'"
            :message="testResult.success ? '文本生成模型测试成功' : '文本生成模型测试失败'"
            :description="testResult.success ? testResult.data?.response || testResult.data?.响应内容 : testResult.error"
            show-icon
          />
          <div v-if="testResult.success && testResult.data" class="test-details">
            <a-descriptions size="small" :column="2">
              <a-descriptions-item label="处理时长">
                {{ testResult.data.processing_time || testResult.data.处理时长 }}s
              </a-descriptions-item>
              <a-descriptions-item label="令牌数量">
                {{ testResult.data.token_count || testResult.data.令牌数量 }}
              </a-descriptions-item>
              <a-descriptions-item v-if="testResult.data.input_tokens" label="输入令牌">
                {{ testResult.data.input_tokens }}
              </a-descriptions-item>
              <a-descriptions-item v-if="testResult.data.output_tokens" label="输出令牌">
                {{ testResult.data.output_tokens }}
              </a-descriptions-item>
            </a-descriptions>
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 配置管理对话框 -->
    <a-modal
      v-model:open="configModalVisible"
      title="配置管理"
      width="700"
      @ok="handleUpdateConfig"
      @cancel="closeConfigModal"
      :confirm-loading="configLoading"
    >
      <div class="config-modal">
        <a-tabs v-model:activeKey="configActiveTab">
          <!-- 阿里云配置 -->
          <a-tab-pane key="aliyun" tab="阿里云百炼配置">
            <a-form layout="vertical">
              <a-form-item label="API密钥">
                <a-input-password
                  v-model:value="aliyunConfig.api_key"
                  placeholder="输入阿里云百炼API密钥..."
                />
              </a-form-item>
              <a-form-item label="基础URL">
                <a-input
                  v-model:value="aliyunConfig.base_url"
                  placeholder="https://dashscope.aliyuncs.com/compatible-mode/v1"
                />
              </a-form-item>
              <a-form-item>
                <a-alert
                  message="配置说明"
                  description="请确保API密钥有效，配置更新后将重新初始化模型管理器。"
                  type="info"
                  show-icon
                />
              </a-form-item>
            </a-form>
          </a-tab-pane>

          <!-- 系统状态 -->
          <a-tab-pane key="status" tab="系统状态">
            <div v-if="managerStatus" class="status-info">
              <a-descriptions :column="1" bordered>
                <a-descriptions-item label="初始化状态">
                  <a-tag :color="managerStatus.已初始化 ? 'green' : 'red'">
                    {{ managerStatus.已初始化 ? '已初始化' : '未初始化' }}
                  </a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="默认模型">
                  {{ managerStatus.默认模型 || '未设置' }}
                </a-descriptions-item>
                <a-descriptions-item label="已加载模型数量">
                  {{ managerStatus.已加载模型数量 }}
                </a-descriptions-item>
                <a-descriptions-item label="可用模型数量">
                  {{ managerStatus.可用模型数量 }}
                </a-descriptions-item>
                <a-descriptions-item label="OpenAI可用">
                  <a-tag :color="managerStatus.OpenAI可用 ? 'green' : 'red'">
                    {{ managerStatus.OpenAI可用 ? '可用' : '不可用' }}
                  </a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="LangChain可用">
                  <a-tag :color="managerStatus.LangChain可用 ? 'green' : 'red'">
                    {{ managerStatus.LangChain可用 ? '可用' : '不可用' }}
                  </a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="阿里云配置状态">
                  <a-tag :color="managerStatus.阿里云配置状态 === '已配置' ? 'green' : 'orange'">
                    {{ managerStatus.阿里云配置状态 }}
                  </a-tag>
                </a-descriptions-item>
              </a-descriptions>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </a-modal>

    <!-- 向量模型测试模态框 -->
    <a-modal
      v-model:open="embeddingTestModalVisible"
      title="向量模型测试"
      width="1200"
      @ok="executeEmbeddingTest"
      @cancel="closeEmbeddingTestModal"
      :confirm-loading="embeddingTestLoading"
    >
      <div v-if="embeddingTestModel" class="embedding-test-modal">
        <a-form layout="vertical">
          <a-form-item label="测试模型">
            <a-input 
              :value="embeddingTestModel.名称 || embeddingTestModel.模型名称 || embeddingTestModel.显示名称 || '未知模型'" 
              disabled 
            />
          </a-form-item>
          
          <a-form-item label="模型信息" v-if="embeddingTestModel">
            <a-descriptions :column="2" size="small" bordered>
              <a-descriptions-item label="模型标识">
                {{ embeddingTestModel.名称 || embeddingTestModel.模型名称 || '未知' }}
              </a-descriptions-item>
              <a-descriptions-item label="模型类型">
                <a-tag color="blue">
                  {{ embeddingTestModel.类型 || embeddingTestModel.模型类型 || '未知类型' }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="提供商">
                {{ embeddingTestModel.提供商 || embeddingTestModel.供应商 || '未知提供商' }}
              </a-descriptions-item>
              <a-descriptions-item label="数据库ID">
                {{ embeddingTestModel.数据库ID || embeddingTestModel.id || 'N/A' }}
              </a-descriptions-item>
            </a-descriptions>
          </a-form-item>

          <a-form-item label="测试文本">
            <div v-for="(text, index) in embeddingTestTexts" :key="index" class="test-text-item">
              <a-textarea
                v-model:value="embeddingTestTexts[index]"
                :placeholder="`输入第${index + 1}个测试文本...`"
                :rows="2"
                :maxlength="100"
                show-count
                style="margin-bottom: 8px;"
              />
              <div class="text-controls">
                <a-button 
                  v-if="index === embeddingTestTexts.length - 1 && embeddingTestTexts.length < 5"
                  type="dashed" 
                  size="small" 
                  @click="addEmbeddingTestText"
                >
                  <plus-outlined />
                  添加文本
                </a-button>
                <a-button 
                  v-if="embeddingTestTexts.length > 2"
                  type="text" 
                  size="small" 
                  danger
                  @click="removeEmbeddingTestText(index)"
                >
                  <delete-outlined />
                  删除
                </a-button>
              </div>
            </div>
          </a-form-item>
        </a-form>

        <!-- 测试过程状态 -->
        <div v-if="embeddingTestLoading" class="test-progress">
          <a-divider>测试进行中</a-divider>
          <a-spin size="large">
            <div style="text-align: center; padding: 20px;">
              <p>正在执行向量模型测试...</p>
              <p style="color: #666; font-size: 12px;">
                这可能需要几秒钟时间，请耐心等待
              </p>
            </div>
          </a-spin>
        </div>

        <!-- 测试错误显示 -->
        <div v-if="embeddingTestError" class="embedding-test-error">
          <a-divider>测试错误</a-divider>
          <a-alert
            type="error"
            :message="embeddingTestError"
            show-icon
            style="margin-bottom: 16px;"
          />
        </div>

        <!-- 测试结果 -->
        <div v-if="embeddingTestResult" class="embedding-test-result">
          <a-divider>测试结果</a-divider>
          
          <!-- 总体状态 -->
          <a-alert
            :type="embeddingTestResult.success ? 'success' : 'error'"
            :message="embeddingTestResult.success ? '向量模型测试成功' : '向量模型测试失败'"
            :description="embeddingTestResult.success ? embeddingTestResult.message : embeddingTestResult.error"
            show-icon
            style="margin-bottom: 16px;"
          />

          <!-- 模型信息和统计 -->
          <a-row :gutter="16" style="margin-bottom: 16px;">
            <a-col :span="12">
              <a-card title="模型信息" size="small">
                <a-descriptions :column="1" size="small">
                  <a-descriptions-item label="模型名称">
                    {{ embeddingTestResult.data?.模型名称 }}
                  </a-descriptions-item>
                  <a-descriptions-item label="模型类型">
                    <a-tag color="cyan">向量模型</a-tag>
                  </a-descriptions-item>
                  <a-descriptions-item label="测试时间">
                    {{ embeddingTestResult.data?.测试时间 }}
                  </a-descriptions-item>
                  <a-descriptions-item label="测试状态">
                    <a-tag :color="embeddingTestResult.success ? 'success' : 'error'">
                      {{ embeddingTestResult.success ? '成功' : '失败' }}
                    </a-tag>
                  </a-descriptions-item>
                </a-descriptions>
              </a-card>
            </a-col>
            <a-col :span="12">
              <a-card title="向量统计" size="small">
                <a-descriptions :column="1" size="small">
                  <a-descriptions-item label="测试文本数量">
                    <a-tag color="green">{{ embeddingTestResult.data?.测试文本数量 }}</a-tag>
                  </a-descriptions-item>
                  <a-descriptions-item label="向量维度">
                    <a-tag color="purple">{{ embeddingTestResult.data?.向量维度 }}</a-tag>
                  </a-descriptions-item>
                  <a-descriptions-item label="向量化状态">
                    <a-tag color="success">
                      {{ embeddingTestResult.success ? '向量化成功' : '向量化失败' }}
                    </a-tag>
                  </a-descriptions-item>
                </a-descriptions>
              </a-card>
            </a-col>
          </a-row>

          <!-- 向量详情 -->
          <a-card title="向量详情" size="small" style="margin-bottom: 16px;">
            <a-table
              :columns="vectorDetailsColumns"
              :data-source="embeddingTestResult.data?.向量详情 || []"
              :pagination="false"
              size="small"
              row-key="序号"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === '状态'">
                  <a-tag :color="record.状态 === '成功' ? 'success' : 'error'">
                    {{ record.状态 }}
                  </a-tag>
                </template>
                <template v-else-if="column.key === '向量预览'">
                  <a-tooltip>
                    <template #title>
                      <div style="max-width: 300px; word-break: break-all;">
                        {{ record.向量预览?.join(', ') }}
                      </div>
                    </template>
                    <a-tag color="cyan">前5个值</a-tag>
                  </a-tooltip>
                </template>
                <template v-else-if="column.key === '测试文本'">
                  <a-tooltip>
                    <template #title>{{ record.测试文本 }}</template>
                    <span style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; display: inline-block;">
                      {{ record.测试文本 }}
                    </span>
                  </a-tooltip>
                </template>
              </template>
            </a-table>
          </a-card>

          <!-- 相似度分析 -->
          <a-card title="相似度分析" size="small">
            <div style="margin-bottom: 12px;">
              <a-descriptions :column="3" size="small">
                <a-descriptions-item label="对比数量">
                  <a-tag color="blue">{{ embeddingTestResult.data?.相似度分析?.对比数量 }}</a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="平均相似度">
                  <a-tag :color="getSimilarityScoreColor(embeddingTestResult.data?.相似度分析?.平均相似度)">
                    {{ embeddingTestResult.data?.相似度分析?.平均相似度 }}
                  </a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="分析状态">
                  <a-tag color="success">
                    {{ embeddingTestResult.data?.相似度分析 ? '分析完成' : '无分析数据' }}
                  </a-tag>
                </a-descriptions-item>
              </a-descriptions>
            </div>
            
            <a-table
              :columns="similarityAnalysisColumns"
              :data-source="embeddingTestResult.data?.相似度分析?.详细对比 || []"
              :pagination="false"
              size="small"
              row-key="序号"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === '相似度评级'">
                  <a-tag :color="getSimilarityColor(record.相似度评级)">
                    {{ record.相似度评级 }}
                  </a-tag>
                </template>
                <template v-else-if="column.key === '余弦相似度'">
                  <a-tag :color="getSimilarityScoreColor(record.余弦相似度)">
                    {{ record.余弦相似度 }}
                  </a-tag>
                </template>
                <template v-else-if="column.key === '状态'">
                  <a-tag :color="record.状态 === '成功' ? 'success' : 'error'">
                    {{ record.状态 }}
                  </a-tag>
                </template>
                <template v-else-if="column.key === '文本1' || column.key === '文本2'">
                  <a-tooltip>
                    <template #title>{{ record[column.key] }}</template>
                    <span style="max-width: 150px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; display: inline-block;">
                      {{ record[column.key] }}
                    </span>
                  </a-tooltip>
                </template>
              </template>
            </a-table>
          </a-card>
        </div>
      </div>
    </a-modal>

    <!-- 向量模型列表模态框 -->
    <a-modal
      v-model:open="embeddingModelsModalVisible"
      title="向量模型列表"
      width="800"
      @ok="closeEmbeddingModelsModal"
      @cancel="closeEmbeddingModelsModal"
    >
      <div v-if="embeddingModelsList.length > 0" class="embedding-models-list">
        <a-table
          :columns="embeddingModelsTableColumns"
          :data-source="embeddingModelsList"
          :loading="embeddingModelsLoading"
          :pagination="embeddingModelsPagination"
          @change="handleEmbeddingModelsChange"
          row-key="数据库ID"
          size="middle"
        >
          <!-- 供应商列 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === '类型'">
              <a-tag :color="getTypeColor(record.类型)">
                {{ record.类型 }}
              </a-tag>
            </template>

            <!-- 状态列 -->
            <template v-else-if="column.key === '状态'">
              <a-tag :color="record.状态 === '已加载' ? 'green' : 'orange'">
                {{ record.状态 }}
              </a-tag>
            </template>

            <!-- 最大令牌列 -->
            <template v-else-if="column.key === '最大令牌数'">
              <span>{{ record.最大令牌数 || record.最大令牌 || 'N/A' }}</span>
            </template>

            <!-- 算力消耗列 -->
            <template v-else-if="column.key === '算力消耗'">
              <a-tag color="blue">{{ record.算力消耗 || 1 }}</a-tag>
            </template>

            <!-- 操作列 -->
            <template v-else-if="column.key === 'actions'">
              <a-space>
                <a-button type="link" size="small" @click="viewEmbeddingModel(record)">
                  <eye-outlined />
                  查看
                </a-button>
                <a-button type="link" size="small" @click="showEmbeddingTestModal(record)">
                  <experiment-outlined />
                  测试
                </a-button>
                <a-button type="link" size="small" @click="editEmbeddingModel(record)">
                  <edit-outlined />
                  编辑
                </a-button>
                <a-button type="link" size="small" danger @click="deleteEmbeddingModel(record)">
                  <delete-outlined />
                  删除
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  SyncOutlined,
  SettingOutlined,
  ExperimentOutlined,
  StarOutlined,
  EditOutlined,
  DeleteOutlined,
  PlusOutlined
} from '@ant-design/icons-vue'
import { modelProviderService } from '../../services/modelProviderService'

export default {
  name: 'cn-模型管理',
  components: {
    SearchOutlined,
    ReloadOutlined,
    EyeOutlined,
    SyncOutlined,
    SettingOutlined,
    ExperimentOutlined,
    StarOutlined,
    EditOutlined,
    DeleteOutlined,
    PlusOutlined
  },
  setup() {
    // 响应式数据
    const loading = ref(false)
    const modelList = ref([])
    const managerStatus = ref(null)
    const searchKeyword = ref('')
    const selectedType = ref(undefined)
    const drawerVisible = ref(false)
    const selectedModel = ref(null)

    // 测试相关数据
    const testModalVisible = ref(false)
    const testingModel = ref(null)
    const testMessage = ref('你好')
    const testResult = ref(null)
    const testLoading = ref(false)
    const testError = ref(null)

    // 配置相关数据
    const configModalVisible = ref(false)
    const configActiveTab = ref('aliyun')
    const configLoading = ref(false)
    const aliyunConfig = reactive({
      api_key: '',
      base_url: 'https://dashscope.aliyuncs.com/compatible-mode/v1'
    })

    // 创建/编辑模型相关数据
    const createModalVisible = ref(false)
    const createLoading = ref(false)
    const isEditing = ref(false)
    const editingModelId = ref(null)
    const createFormRef = ref(null)
    const modelParamsText = ref('')

    // 向量模型测试相关数据
    const embeddingTestModalVisible = ref(false)
    const embeddingTestLoading = ref(false)
    const embeddingTestModel = ref(null)
    const embeddingTestTexts = ref([''])
    const embeddingTestResult = ref(null)
    const embeddingTestError = ref(null)
    
    // 向量模型列表相关数据
    const embeddingModelsModalVisible = ref(false)
    const embeddingModelsList = ref([])
    const embeddingModelsLoading = ref(false)

    // 向量详情表格列定义
    const vectorDetailsColumns = [
      {
        title: '序号',
        dataIndex: '序号',
        key: '序号',
        width: 60,
        align: 'center'
      },
      {
        title: '测试文本',
        dataIndex: '测试文本',
        key: '测试文本',
        width: 200,
        ellipsis: true
      },
      {
        title: '向量维度',
        dataIndex: '向量维度',
        key: '向量维度',
        width: 100,
        align: 'center'
      },
      {
        title: '向量范数',
        dataIndex: '向量范数',
        key: '向量范数',
        width: 100,
        align: 'center'
      },
      {
        title: '向量预览',
        dataIndex: '向量预览',
        key: '向量预览',
        width: 120,
        align: 'center'
      },
      {
        title: '状态',
        dataIndex: '状态',
        key: '状态',
        width: 80,
        align: 'center'
      }
    ]

    // 相似度分析表格列定义
    const similarityAnalysisColumns = [
      {
        title: '序号',
        dataIndex: '序号',
        key: '序号',
        width: 60,
        align: 'center'
      },
      {
        title: '文本1',
        dataIndex: '文本1',
        key: '文本1',
        width: 150,
        ellipsis: true
      },
      {
        title: '文本2',
        dataIndex: '文本2',
        key: '文本2',
        width: 150,
        ellipsis: true
      },
      {
        title: '余弦相似度',
        dataIndex: '余弦相似度',
        key: '余弦相似度',
        width: 120,
        align: 'center'
      },
      {
        title: '相似度评级',
        dataIndex: '相似度评级',
        key: '相似度评级',
        width: 100,
        align: 'center'
      },
      {
        title: '状态',
        dataIndex: '状态',
        key: '状态',
        width: 80,
        align: 'center'
      }
    ]

    // 向量模型表格配置
    const embeddingModelsTableColumns = [
      {
        title: '模型名称',
        dataIndex: '显示名称',
        key: '显示名称',
        width: 200,
        ellipsis: true
      },
      {
        title: '供应商',
        dataIndex: '提供商',
        key: '提供商',
        width: 120
      },
      {
        title: '模型类型',
        dataIndex: '模型类型',
        key: '类型',
        width: 150
      },
      {
        title: '状态',
        dataIndex: '状态',
        key: '状态',
        width: 100
      },
      {
        title: '调用统计',
        key: '调用统计',
        width: 150,
        customRender: ({ record }) => {
          return `${record.调用统计?.成功率 || '0%'}`
        }
      },
      {
        title: '操作',
        key: 'actions',
        width: 200,
        fixed: 'right'
      }
    ]

    // 向量模型分页配置
    const embeddingModelsPagination = computed(() => ({
      current: 1,
      pageSize: 10,
      total: embeddingModelsList.value.length,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total) => `共 ${total} 条记录`
    }))

    // 创建模型表单数据
    const createForm = reactive({
      供应商名称: '',
      模型名称: '',
      模型类型: '',
      模型功能类型: 'chat',
      API密钥: '',
      API基础URL: '',
      模型参数: {},
      是否启用: true,
      备注: ''
    })

    // 表单验证规则
    const createFormRules = {
      供应商名称: [
        { required: true, message: '请选择供应商名称', trigger: 'change' }
      ],
      模型名称: [
        { required: true, message: '请输入模型名称', trigger: 'blur' },
        { min: 1, max: 100, message: '模型名称长度应在1-100字符之间', trigger: 'blur' }
      ],
      模型类型: [
        { required: true, message: '请选择模型类型', trigger: 'change' }
      ],
      模型功能类型: [
        { required: true, message: '请选择模型功能类型', trigger: 'change' }
      ],
      API密钥: [
        { required: true, message: '请输入API密钥', trigger: 'blur' },
        { min: 1, max: 500, message: 'API密钥长度应在1-500字符之间', trigger: 'blur' }
      ]
    }

    // 模型功能类型选项
    const modelFunctionTypes = [
      { value: 'chat', label: '对话模型 (Chat)', description: '用于文本生成和对话' },
      { value: 'embedding', label: '向量模型 (Embedding)', description: '用于文本向量化和语义搜索' }
    ]

    // 供应商选项
    const providerOptions = [
      { value: 'OpenAI', label: 'OpenAI' },
      { value: '阿里云', label: '阿里云' },
      { value: '阿里云百炼', label: '阿里云百炼' },
      { value: 'Anthropic', label: 'Anthropic' },
      { value: 'Google', label: 'Google' },
      { value: '本地模型', label: '本地模型' }
    ]

    // 分页配置
    const pagination = reactive({
      current: 1,
      pageSize: 20,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
    })

    // 表格列配置
    const tableColumns = [
      {
        title: '模型名称',
        dataIndex: '显示名称',
        key: '显示名称',
        width: 200
      },
      {
        title: '模型标识',
        dataIndex: '模型名称',
        key: '模型名称',
        width: 150
      },
      {
        title: '供应商',
        dataIndex: '提供商',
        key: '提供商',
        width: 120
      },
      {
        title: '模型类型',
        dataIndex: '模型类型',
        key: '模型类型',
        width: 120
      },
      {
        title: '状态',
        dataIndex: '状态',
        key: '状态',
        width: 80
      },
      {
        title: '最大令牌',
        dataIndex: '最大令牌数',
        key: '最大令牌数',
        width: 100
      },
      {
        title: '调用次数',
        dataIndex: '调用次数',
        key: '调用次数',
        width: 100
      },
      {
        title: '成功率',
        key: '成功率',
        width: 100,
        customRender: ({ record }) => {
          const total = record.调用次数 || 0
          const success = record.成功次数 || 0
          if (total === 0) return '0%'
          return `${Math.round((success / total) * 100)}%`
        }
      },
      {
        title: '操作',
        key: 'actions',
        width: 200,
        fixed: 'right'
      }
    ]

    // 计算属性
    const paginationConfig = computed(() => ({
      ...pagination,
      onChange: (page, pageSize) => {
        pagination.current = page
        pagination.pageSize = pageSize
        loadModelList()
      },
      onShowSizeChange: (current, size) => {
        pagination.current = 1
        pagination.pageSize = size
        loadModelList()
      }
    }))

    // 统计数据
    const totalModels = computed(() => modelList.value.length)
    const availableModels = computed(() => modelList.value.filter(m => m.状态 === '已加载').length)

    // 模型类型列表（用于筛选）
    const modelTypes = computed(() => {
      const types = [...new Set(modelList.value.map(m => m.类型))]
      return types.filter(t => t && t !== '未知')
    })

    // 方法
    const loadModelList = async () => {
      loading.value = true
      try {
        const 参数 = {
          页码: pagination.current,
          每页数量: pagination.pageSize,
          模型类型: selectedType.value,
          是否启用: true  // 默认只显示启用的模型
        }

        const 结果 = await modelProviderService.getModelList(参数)

        // 调试信息
        console.log('🔍 API调用结果:', 结果)

        if (结果.success) {
          modelList.value = 结果.data.列表 || []
          pagination.total = 结果.data.总数 || 0
          console.log('✅ 模型列表加载成功，数量:', modelList.value.length)
        } else {
          console.error('❌ API调用失败:', 结果)
          message.error(结果.error || '获取模型列表失败')
          modelList.value = []
          pagination.total = 0
        }
      } catch (错误) {
        message.error('加载模型列表失败')
        modelList.value = []
        pagination.total = 0
      } finally {
        loading.value = false
      }
    }

    const refreshStatus = async () => {
      try {
        const 结果 = await modelProviderService.getModelManagerStatus()
        if (结果.success) {
          managerStatus.value = 结果.data
        } else {
          message.error(结果.error || '获取状态失败')
        }
      } catch (错误) {
        message.error('刷新状态失败')
      }
    }

    const refreshModelConfig = async () => {
      try {
        loading.value = true
        const 结果 = await modelProviderService.refreshModelConfig()

        if (结果.success) {
          message.success(结果.message || '模型配置刷新成功')
          await loadModelList()
          await refreshStatus()
        } else {
          message.error(结果.error || '刷新模型配置失败')
        }
      } catch (错误) {
        message.error('刷新模型配置失败')
      } finally {
        loading.value = false
      }
    }

    const handleSearch = () => {
      pagination.current = 1
      loadModelList()
    }

    const handleReset = () => {
      searchKeyword.value = ''
      selectedType.value = undefined
      pagination.current = 1
      loadModelList()
    }

    const handleTypeChange = () => {
      pagination.current = 1
      loadModelList()
    }

    const handleTableChange = (pag, filters, sorter) => {
      pagination.current = pag.current
      pagination.pageSize = pag.pageSize
      loadModelList()
    }

    const viewModel = (模型) => {
      selectedModel.value = 模型
      drawerVisible.value = true
    }

    const closeDrawer = () => {
      drawerVisible.value = false
      selectedModel.value = null
    }

    const getTypeColor = (类型) => {
      const 颜色映射 = {
        '阿里云百炼': 'orange',
        '阿里云': 'orange',
        'OpenAI': 'green',
        'Anthropic': 'blue',
        'Google': 'red',
        'Microsoft': 'geekblue',
        '未知': 'default'
      }
      return 颜色映射[类型] || 'purple'
    }

    const getModelTypeColor = (模型类型) => {
      if (!模型类型) return 'default'

      const 类型小写 = 模型类型.toLowerCase()
      if (类型小写.includes('embedding') || 类型小写.includes('向量')) {
        return 'cyan'  // 向量模型用青色
      } else if (类型小写.includes('chat') || 类型小写.includes('对话') || 类型小写.includes('生成')) {
        return 'green'  // 对话模型用绿色
      } else {
        return 'purple'  // 其他类型用紫色
      }
    }

    // 测试模型
    const testModel = (模型) => {
      testingModel.value = 模型
      testMessage.value = '你好'
      testResult.value = null
      testError.value = null
      testModalVisible.value = true
    }

    // 处理测试模型
    const handleTestModel = async () => {
      if (!testingModel.value) return

      try {
        testLoading.value = true
        const 结果 = await modelProviderService.testModelConnection({
          模型id: testingModel.value.数据库ID || testingModel.value.id,
          测试消息: testMessage.value
        })

        console.log('文本生成模型测试结果:', 结果)

        // 根据后端统一响应格式解析
        if (结果.status === 100) {
          testResult.value = {
            success: true,
            type: 'chat',
            data: 结果.data,
            message: 结果.message
          }
          message.success(结果.message || '文本生成模型测试成功')
        } else {
          testResult.value = {
            success: false,
            type: 'chat',
            error: 结果.message || '文本生成模型测试失败'
          }
          testError.value = 结果.message || '文本生成模型测试失败'
          message.error(结果.message || '文本生成模型测试失败')
        }
      } catch (error) {
        console.error('测试模型失败:', error)
        testResult.value = null
        testError.value = error.message || '测试请求失败'
        message.error('测试模型失败')
      } finally {
        testLoading.value = false
      }
    }

    // 关闭测试对话框
    const closeTestModal = () => {
      testModalVisible.value = false
      testingModel.value = null
      testResult.value = null
      testError.value = null
    }

    // 设为默认模型
    const setAsDefault = async (模型) => {
      try {
        const 结果 = await modelProviderService.setDefaultModel(模型.模型名 || 模型.模型标识)

        if (结果.success) {
          message.success(`已将 ${模型.名称} 设为默认模型`)
          await refreshStatus()
        } else {
          message.error(结果.error || '设置默认模型失败')
        }
      } catch (错误) {
        message.error('设置默认模型失败')
      }
    }

    // 显示配置对话框
    const showConfigModal = () => {
      configModalVisible.value = true
      configActiveTab.value = 'aliyun'
    }

    // 处理更新配置
    const handleUpdateConfig = async () => {
      try {
        configLoading.value = true

        if (configActiveTab.value === 'aliyun') {
          const result = await modelProviderService.updateAliyunConfig(aliyunConfig)

          if (result.success) {
            message.success('阿里云配置更新成功')
            await refreshStatus()
            configModalVisible.value = false
          } else {
            message.error(result.error || '更新配置失败')
          }
        }
      } catch (error) {
        console.error('更新配置失败:', error)
        message.error('更新配置失败')
      } finally {
        configLoading.value = false
      }
    }

    // 关闭配置对话框
    const closeConfigModal = () => {
      configModalVisible.value = false
    }

    // ==================== 创建/编辑/删除模型相关方法 ====================

    // 显示创建模型模态框
    const showCreateModal = () => {
      isEditing.value = false
      editingModelId.value = null
      resetCreateForm()
      createModalVisible.value = true
    }

    // 显示编辑模型模态框
    const editModel = (模型) => {
      isEditing.value = true
      editingModelId.value = 模型.数据库ID
      
      // 填充表单数据
      createForm.供应商名称 = 模型.类型
      createForm.模型名称 = 模型.模型名 || 模型.模型标识
      createForm.模型类型 = 模型.类型
      createForm.API密钥 = '***' // 安全考虑，不显示真实密钥
      createForm.API基础URL = 模型.API基础URL || ''
      createForm.模型参数 = 模型.默认参数 || {}
      createForm.是否启用 = 模型.状态 === '已加载'
      createForm.备注 = 模型.描述 || ''

      // 设置模型参数文本
      if (模型.默认参数 && Object.keys(模型.默认参数).length > 0) {
        modelParamsText.value = JSON.stringify(模型.默认参数, null, 2)
      } else {
        modelParamsText.value = ''
      }

      createModalVisible.value = true
    }

    // 重置创建表单
    const resetCreateForm = () => {
      Object.assign(createForm, {
        供应商名称: '',
        模型名称: '',
        模型类型: '',
        API密钥: '',
        API基础URL: '',
        模型参数: {},
        是否启用: true,
        备注: ''
      })
      modelParamsText.value = ''
    }

    // 处理供应商变化
    const handleProviderChange = (value) => {
      createForm.模型类型 = value
    }

    // 处理模型功能类型变化
    const handleFunctionTypeChange = (value) => {
      // 根据功能类型自动调整模型参数
      if (value === 'embedding') {
        // 向量模型的默认参数
        const embeddingParams = {
          model_type: 'embedding',
          embedding_dimension: 1536,
          batch_size: 32
        }
        modelParamsText.value = JSON.stringify(embeddingParams, null, 2)
        createForm.模型参数 = embeddingParams
      } else {
        // 对话模型的默认参数
        const chatParams = {
          model_type: 'chat',
          temperature: 0.7,
          max_tokens: 4000,
          top_p: 0.9
        }
        modelParamsText.value = JSON.stringify(chatParams, null, 2)
        createForm.模型参数 = chatParams
      }
    }

    // 解析模型参数JSON
    const parseModelParams = () => {
      if (!modelParamsText.value.trim()) {
        createForm.模型参数 = {}
        return
      }

      try {
        const parsed = JSON.parse(modelParamsText.value)
        createForm.模型参数 = parsed
      } catch (error) {
        message.warning('模型参数JSON格式不正确，将使用空对象')
        createForm.模型参数 = {}
      }
    }

    // 处理创建/编辑模型
    const handleCreateModel = async () => {
      try {
        // 表单验证
        await createFormRef.value.validate()
        
        createLoading.value = true

        // 解析模型参数
        parseModelParams()

        if (isEditing.value) {
          // 编辑模式
          const 更新数据 = {
            供应商名称: createForm.供应商名称,
            模型名称: createForm.模型名称,
            模型类型: createForm.模型类型,
            API基础URL: createForm.API基础URL || null,
            模型参数: createForm.模型参数,
            是否启用: createForm.是否启用,
            备注: createForm.备注 || null
          }

          // 只有当密钥不是占位符时才更新
          if (createForm.API密钥 && createForm.API密钥 !== '***') {
            更新数据.API密钥 = createForm.API密钥
          }

          const 结果 = await modelProviderService.updateModel(editingModelId.value, 更新数据)

          if (结果.success) {
            message.success('模型配置更新成功')
            createModalVisible.value = false
            await loadModelList()
            await refreshStatus()
          } else {
            message.error(结果.error || '更新模型配置失败')
          }
        } else {
          // 创建模式
          const 结果 = await modelProviderService.createModel(createForm)

          if (结果.success) {
            message.success('模型配置创建成功')
            createModalVisible.value = false
            await loadModelList()
            await refreshStatus()
          } else {
            message.error(结果.error || '创建模型配置失败')
          }
        }
      } catch (error) {
        if (error.errorFields) {
          message.error('请检查表单输入')
        } else {
          console.error('创建/编辑模型失败:', error)
          message.error('操作失败')
        }
      } finally {
        createLoading.value = false
      }
    }

    // 关闭创建模型模态框
    const closeCreateModal = () => {
      createModalVisible.value = false
      resetCreateForm()
    }

    // 删除模型
    const deleteModel = (模型) => {
      Modal.confirm({
        title: '确认删除',
        content: `确定要删除模型 "${模型.名称}" 吗？此操作不可恢复。`,
        okText: '确认删除',
        okType: 'danger',
        cancelText: '取消',
        onOk: async () => {
          try {
            const 结果 = await modelProviderService.deleteModel(模型.数据库ID)

            if (结果.success) {
              message.success('模型配置删除成功')
              await loadModelList()
              await refreshStatus()
            } else {
              message.error(结果.error || '删除模型配置失败')
            }
          } catch (error) {
            console.error('删除模型失败:', error)
            message.error('删除模型失败')
          }
        }
      })
    }

    // ==================== 向量模型测试相关方法 ====================

    // 显示向量模型测试模态框
    const showEmbeddingTestModal = (模型) => {
      console.log('尝试打开向量模型测试:', 模型)
      
      // 改进向量模型识别逻辑
      const 是向量模型 = (
        模型.类型?.toLowerCase().includes('embedding') ||
        模型.模型类型?.toLowerCase().includes('embedding') ||
        模型.功能类型?.toLowerCase().includes('embedding') ||
        模型.名称?.toLowerCase().includes('embedding') ||
        模型.模型名称?.toLowerCase().includes('embedding')
      )
      
      console.log('向量模型检查结果:', {
        模型类型: 模型.类型,
        模型功能类型: 模型.功能类型,
        模型名称: 模型.名称,
        是向量模型
      })
      
      if (!是向量模型) {
        message.warning('该模型不是向量模型，无法进行向量测试')
        return
      }

      embeddingTestModel.value = 模型
      embeddingTestTexts.value = [
        '人工智能是计算机科学的一个分支',
        '机器学习是人工智能的重要组成部分',
        '深度学习是机器学习的一个子领域'
      ]
      embeddingTestResult.value = null
      embeddingTestModalVisible.value = true
      
      console.log('向量模型测试模态框已打开')
    }

    // 添加测试文本
    const addEmbeddingTestText = () => {
      if (embeddingTestTexts.value.length < 10) {
        embeddingTestTexts.value.push('')
      } else {
        message.warning('最多只能添加10个测试文本')
      }
    }

    // 删除测试文本
    const removeEmbeddingTestText = (index) => {
      if (embeddingTestTexts.value.length > 2) {
        embeddingTestTexts.value.splice(index, 1)
      } else {
        message.warning('至少需要保留2个测试文本')
      }
    }

    // 执行向量模型测试
    const executeEmbeddingTest = async () => {
      try {
        // 验证测试文本
        const 有效文本 = embeddingTestTexts.value.filter(text => text.trim())
        if (有效文本.length < 2) {
          message.error('至少需要2个有效的测试文本')
          return
        }

        embeddingTestLoading.value = true
        embeddingTestError.value = null // 清空之前的错误

        console.log('开始向量模型测试:', {
          模型: embeddingTestModel.value,
          测试文本: 有效文本
        })

        const 测试数据 = {
          模型id: embeddingTestModel.value.数据库ID,
          模型名称: embeddingTestModel.value.名称 || embeddingTestModel.value.模型名称,
          测试文本列表: 有效文本
        }

        console.log('发送测试请求:', 测试数据)

        const 结果 = await modelProviderService.testEmbeddingModel(测试数据)

        console.log('向量模型测试结果:', 结果)

        // 根据后端统一响应格式解析
        if (结果.status === 100) {
          embeddingTestResult.value = {
            success: true,
            type: 'embedding',
            data: 结果.data,
            message: 结果.message
          }
          embeddingTestError.value = null

          console.log('解析后的向量测试结果:', embeddingTestResult.value)

          message.success(结果.message || '向量模型测试完成')
        } else {
          console.error('向量模型测试失败:', 结果.message)
          embeddingTestResult.value = {
            success: false,
            type: 'embedding',
            error: 结果.message || '向量模型测试失败'
          }
          embeddingTestError.value = 结果.message || '向量模型测试失败'
          message.error(结果.message || '向量模型测试失败')
        }
      } catch (error) {
        console.error('向量模型测试异常:', error)
        embeddingTestResult.value = null
        embeddingTestError.value = error.message || '向量模型测试异常'
        message.error('向量模型测试失败')
      } finally {
        embeddingTestLoading.value = false
      }
    }

    // 关闭向量测试模态框
    const closeEmbeddingTestModal = () => {
      embeddingTestModalVisible.value = false
      embeddingTestModel.value = null
      embeddingTestTexts.value = ['']
      embeddingTestResult.value = null
      embeddingTestError.value = null
    }

    // 显示向量模型列表
    const showEmbeddingModelsList = async () => {
      try {
        embeddingModelsLoading.value = true
        embeddingModelsModalVisible.value = true

        const 结果 = await modelProviderService.getEmbeddingModelList()

        if (结果.success) {
          embeddingModelsList.value = 结果.data.向量模型列表 || []
        } else {
          message.error(结果.error || '获取向量模型列表失败')
          embeddingModelsList.value = []
        }
      } catch (error) {
        console.error('获取向量模型列表失败:', error)
        message.error('获取向量模型列表失败')
        embeddingModelsList.value = []
      } finally {
        embeddingModelsLoading.value = false
      }
    }

    // 关闭向量模型列表模态框
    const closeEmbeddingModelsModal = () => {
      embeddingModelsModalVisible.value = false
      embeddingModelsList.value = []
    }

    // 获取相似度评级颜色
    const getSimilarityColor = (评级) => {
      switch (评级) {
        case '高': return 'success'
        case '中': return 'warning'
        case '低': return 'error'
        default: return 'default'
      }
    }

    // 获取相似度分数颜色
    const getSimilarityScoreColor = (分数) => {
      const score = Math.abs(parseFloat(分数) || 0)
      if (score >= 0.7) return 'success'
      if (score >= 0.3) return 'warning'
      return 'error'
    }

    // 处理向量模型表格变化
    const handleEmbeddingModelsChange = (pagination, filters, sorter) => {
      console.log('向量模型表格变化:', { pagination, filters, sorter })
    }

    // 查看向量模型详情
    const viewEmbeddingModel = (模型) => {
      // 复用现有的查看模型方法
      viewModel(模型)
    }

    // 编辑向量模型
    const editEmbeddingModel = (模型) => {
      // 复用现有的编辑模型方法
      editModel(模型)
    }

    // 删除向量模型
    const deleteEmbeddingModel = (模型) => {
      // 复用现有的删除模型方法
      deleteModel(模型)
    }

    // 组件挂载时初始化
    onMounted(async () => {
      await Promise.all([
        loadModelList(),
        refreshStatus()
      ])
    })

    return {
      // 响应式数据
      loading,
      modelList,
      managerStatus,
      searchKeyword,
      selectedType,
      drawerVisible,
      selectedModel,
      tableColumns,
      paginationConfig,
      totalModels,
      availableModels,
      modelTypes,

      // 测试相关
      testModalVisible,
      testingModel,
      testMessage,
      testResult,
      testLoading,
      testError,

      // 配置相关
      configModalVisible,
      configActiveTab,
      configLoading,
      aliyunConfig,

      // 创建/编辑模型相关
      createModalVisible,
      createLoading,
      isEditing,
      editingModelId,
      createFormRef,
      modelParamsText,
      createForm,
      createFormRules,

      // 模型功能类型选项
      modelFunctionTypes,

      // 供应商选项
      providerOptions,

      // 方法
      loadModelList,
      refreshStatus,
      refreshModelConfig,
      handleSearch,
      handleReset,
      handleTypeChange,
      handleTableChange,
      getTypeColor,
      getModelTypeColor,
      viewModel,
      closeDrawer,
      testModel,
      handleTestModel,
      closeTestModal,
      setAsDefault,
      setDefaultModel: setAsDefault,
      showConfigModal,
      handleUpdateConfig,
      closeConfigModal,

      // 创建/编辑/删除模型方法
      showCreateModal,
      editModel,
      resetCreateForm,
      handleCreateModel,
      closeCreateModal,
      deleteModel,
      handleProviderChange,
      parseModelParams,

      // 处理模型功能类型变化
      handleFunctionTypeChange: (value) => {
        // 根据功能类型自动调整模型参数
        if (value === 'embedding') {
          // 向量模型的默认参数
          const embeddingParams = {
            model_type: 'embedding',
            embedding_dimension: 1536,
            batch_size: 32
          }
          modelParamsText.value = JSON.stringify(embeddingParams, null, 2)
          createForm.模型参数 = embeddingParams
        } else {
          // 对话模型的默认参数
          const chatParams = {
            model_type: 'chat',
            temperature: 0.7,
            max_tokens: 4000,
            top_p: 0.9
          }
          modelParamsText.value = JSON.stringify(chatParams, null, 2)
          createForm.模型参数 = chatParams
        }
      },

      // 向量模型测试相关方法
      embeddingTestModalVisible,
      embeddingTestLoading,
      embeddingTestModel,
      embeddingTestTexts,
      embeddingTestResult,
      embeddingTestError,
      vectorDetailsColumns,
      similarityAnalysisColumns,
      showEmbeddingTestModal,
      addEmbeddingTestText,
      removeEmbeddingTestText,
      executeEmbeddingTest,
      closeEmbeddingTestModal,
      getSimilarityScoreColor,
      
      // 向量模型列表相关
      embeddingModelsModalVisible,
      embeddingModelsList,
      embeddingModelsLoading,
      showEmbeddingModelsList,
      closeEmbeddingModelsModal,
      getSimilarityColor,
      embeddingModelsTableColumns,
      embeddingModelsPagination,
      handleEmbeddingModelsChange,
      viewEmbeddingModel,
      editEmbeddingModel,
      deleteEmbeddingModel
    }
  }
}
</script>

<style scoped>
.model-management {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.page-description {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stats-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  text-align: center;
}

.stats-number {
  font-size: 28px;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 8px;
}

.stats-label {
  font-size: 14px;
  color: #8c8c8c;
}

.filter-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  margin-bottom: 16px;
}

.filter-row {
  display: flex;
  gap: 16px;
  align-items: center;
}

.table-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  overflow: hidden;
}

.model-name-cell {
  display: flex;
  flex-direction: column;
}

.model-title {
  font-weight: 500;
  color: #262626;
}

.model-subtitle {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 2px;
}

.features-cell {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.model-detail {
  padding: 8px 0;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

.features-list {
  display: flex;
  flex-wrap: wrap;
}

.detail-section pre {
  background: #f8f8f8;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
}

/* 新增样式 */
.test-modal {
  padding: 8px 0;
}

.test-result {
  margin-top: 16px;
}

.test-result h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.test-details {
  margin-top: 12px;
}

.config-modal {
  padding: 8px 0;
}

.status-info {
  padding: 8px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .model-management {
    padding: 16px;
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }

  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 创建模型表单样式 */
.form-help {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 4px;
}

.create-modal .ant-form-item {
  margin-bottom: 16px;
}

.create-modal .ant-form-item-label {
  font-weight: 500;
}

.create-modal .ant-input,
.create-modal .ant-select,
.create-modal .ant-input-password {
  border-radius: 6px;
}

.create-modal .ant-input:focus,
.create-modal .ant-select:focus,
.create-modal .ant-input-password:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.create-modal .ant-switch {
  background-color: #00b96b;
}

.create-modal .ant-switch:hover:not(.ant-switch-disabled) {
  background-color: #389e0d;
}

/* 向量测试相关样式 */
.test-text-item {
  margin-bottom: 12px;
}

.text-controls {
  display: flex;
  gap: 8px;
  margin-top: 8px;
  justify-content: flex-start;
}

.embedding-test-result {
  margin-top: 20px;
}

.embedding-test-result .ant-card {
  margin-bottom: 0;
}

.embedding-test-result .ant-table-tbody > tr > td {
  padding: 8px 12px;
}

.embedding-test-result .ant-descriptions-item-label {
  font-weight: 500;
  color: #262626;
}

.embedding-test-result .ant-descriptions-item-content {
  color: #595959;
}

.embedding-test-error {
  margin-bottom: 16px;
}
</style>