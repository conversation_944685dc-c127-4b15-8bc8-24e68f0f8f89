<template>
  <div class="output-config">
    <div class="section-header">
      <h2>🔧 结构化输出配置</h2>
      <p>配置智能体的输出格式和结构</p>
    </div>
    
    <a-form layout="vertical" class="config-form">
      <a-card title="输出格式设置" class="config-card">
        <!-- 输出格式选择 -->
        <a-form-item label="输出格式">
          <a-radio-group v-model:value="localForm.输出格式" @change="handleOutputModeChange">
            <a-radio value="text">
              <div class="radio-option">
                <div class="option-title">📝 纯文本</div>
                <div class="option-desc">自然语言回复，适合对话场景</div>
              </div>
            </a-radio>
            <a-radio value="json">
              <div class="radio-option">
                <div class="option-title">🔧 JSON格式</div>
                <div class="option-desc">结构化数据输出，适合API调用</div>
              </div>
            </a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- JSON格式配置 -->
        <div v-if="localForm.输出格式 === 'json'" class="json-output-config">
          <a-tabs v-model:activeKey="schemaEditMode" size="small">
            <a-tab-pane key="visual" tab="🎨 可视化构建">
              <NestedSchemaBuilder
                v-model="localForm.自定义回复格式"
                @update:modelValue="handleFormChange"
              />
            </a-tab-pane>
            <a-tab-pane key="code" tab="📝 代码编辑">
              <a-form-item label="JSON Schema">
                <a-textarea
                  v-model:value="schemaText"
                  placeholder="定义JSON输出的结构..."
                  :rows="12"
                  @change="handleSchemaTextChange"
                />
                <div class="schema-help">
                  <a-typography-text type="secondary">
                    定义智能体输出的JSON结构。例如：
                  </a-typography-text>
                  <pre class="schema-example">{
  "type": "object",
  "properties": {
    "answer": {"type": "string", "description": "回答内容"},
    "confidence": {"type": "number", "description": "置信度"}
  },
  "required": ["answer"]
}</pre>
                </div>
              </a-form-item>
            </a-tab-pane>
          </a-tabs>
        </div>
      </a-card>
    </a-form>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import NestedSchemaBuilder from './NestedSchemaBuilder.vue'

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  },
  errors: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'validate', 'change'])

// 本地状态
const schemaEditMode = ref('visual')

// Schema文本编辑
const schemaText = computed({
  get: () => {
    const schema = localForm.value.自定义回复格式
    if (!schema) return ''
    return typeof schema === 'object' ? JSON.stringify(schema, null, 2) : schema
  },
  set: (value) => {
    try {
      localForm.value.自定义回复格式 = value ? JSON.parse(value) : null
    } catch (error) {
      // 保持字符串格式，等待用户修正
      localForm.value.自定义回复格式 = value
    }
  }
})

// 处理Schema文本变化
const handleSchemaTextChange = () => {
  handleFormChange()
}

// 本地表单数据
const localForm = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})









// 处理输出格式变化
const handleOutputModeChange = () => {
  if (localForm.value.输出格式 === 'text') {
    // 纯文本模式，清空JSON Schema
    localForm.value.自定义回复格式 = null
  } else if (localForm.value.输出格式 === 'json') {
    // JSON模式，设置默认Schema
    if (!localForm.value.自定义回复格式) {
      localForm.value.自定义回复格式 = JSON.stringify({
        type: "object",
        properties: {
          content: {
            type: "string",
            description: "回复内容"
          }
        },
        required: ["content"]
      }, null, 2)
    }
  }
  handleFormChange()
}



// 处理表单变化
const handleFormChange = () => {
  emit('change', localForm.value)
}

// 初始化默认值
if (!localForm.value.输出格式) localForm.value.输出格式 = 'text'
if (!localForm.value.最大输出长度) localForm.value.最大输出长度 = 2000
if (!localForm.value.输出语言) localForm.value.输出语言 = 'zh-CN'
</script>

<style scoped>
.output-config {
  padding: 16px;
}

.section-header {
  margin-bottom: 24px;
}

.section-header h2 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-header p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.config-form {
  max-width: 800px;
}

.config-card {
  margin-bottom: 16px;
}

.mode-desc {
  margin-top: 4px;
  color: #8c8c8c;
  font-size: 12px;
}

.structured-output {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.schema-editor {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
}

.editor-toolbar {
  padding: 8px 12px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.schema-textarea {
  border: none;
  border-radius: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.6;
}

.schema-textarea:focus {
  box-shadow: none;
}

.schema-stats {
  padding: 8px 12px;
  background: #fafafa;
  border-top: 1px solid #f0f0f0;
  font-size: 12px;
}

.schema-preview {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  overflow: hidden;
}

.preview-code {
  background: #f5f5f5;
  padding: 16px;
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.6;
  overflow-x: auto;
}

.structure-info {
  padding: 16px;
}

.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 16px;
}

.template-card {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.template-card:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

.template-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.template-desc {
  color: #8c8c8c;
  font-size: 12px;
  margin-bottom: 12px;
}

.template-preview {
  background: #f5f5f5;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 8px;
  font-size: 11px;
  line-height: 1.4;
  max-height: 120px;
  overflow-y: auto;
}

/* 新增：输出格式选项样式 */
.radio-option {
  padding: 8px 0;
}

.option-title {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.option-desc {
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.4;
}

/* 优化单选框组样式 */
:deep(.ant-radio-group) {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

:deep(.ant-radio-wrapper) {
  padding: 12px 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  margin: 0;
  transition: all 0.3s ease;
}

:deep(.ant-radio-wrapper:hover) {
  border-color: #1890ff;
  background: #f6f9ff;
}

:deep(.ant-radio-wrapper-checked) {
  border-color: #1890ff;
  background: #f6f9ff;
}

/* JSON配置区域样式 */
.json-output-config {
  margin-top: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  overflow: hidden;
}

.json-output-config :deep(.ant-tabs-content-holder) {
  padding: 0;
}

.json-output-config :deep(.ant-tabs-tabpane) {
  padding: 16px;
  background: #fafafa;
}

.json-output-config :deep(.ant-tabs-tabpane):first-child {
  padding: 0;
  background: transparent;
}

.schema-help {
  margin-top: 8px;
}

.schema-example {
  background: #f5f5f5;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 12px;
  margin-top: 8px;
  font-size: 12px;
  line-height: 1.4;
  color: #666;
}
</style>
